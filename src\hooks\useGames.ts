"use client";

import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function useGames(options?: {
  gameType?: "poker" | "blackjack" | "uno" | "gofish";
  featured?: boolean;
  limit?: number;
}) {
  const games = useQuery(api.games.list, options || {});
  return {
    games,
    loading: games === undefined,
  };
}

export function useFeaturedGames(limit?: number) {
  const games = useQuery(api.games.getFeatured, { limit });
  return {
    games,
    loading: games === undefined,
  };
}

export function useGame(id: string) {
  return useQuery(api.games.get, { id: id as any });
}

export function useGameSearch(query: string, options?: {
  gameType?: "poker" | "blackjack" | "uno" | "gofish";
  limit?: number;
}) {
  return useQuery(api.games.search, { query, ...options });
}

export function useGameStats(gameId: string) {
  return useQuery(api.games.getStats, { gameId: gameId as any });
}