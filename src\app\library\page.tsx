"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { GameCard } from "@/components/game/game-card";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/useAuth";
import { useGames } from "@/hooks/useGames";
import { Search, Grid, List, Clock, Trophy, Star, Filter } from "lucide-react";

export default function LibraryPage() {
  const { user } = useAuth();
  const { games, loading } = useGames();
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"recent" | "name" | "playtime">("recent");

  // For demo purposes, assume user owns the first 3 games
  const ownedGames = games?.slice(0, 3) || [];
  const recentlyPlayed = ownedGames.slice(0, 2);
  const favorites = ownedGames.slice(0, 1);

  const filteredGames = ownedGames.filter(game =>
    game.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    game.genre.some(g => g.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const sortedGames = [...filteredGames].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.title.localeCompare(b.title);
      case "playtime":
        // For demo, use rating as proxy for playtime
        return b.rating - a.rating;
      case "recent":
      default:
        return new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime();
    }
  });

  const totalPlaytime = user?.totalPlaytime || 0;
  const formatPlaytime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    return hours > 0 ? `${hours}h` : `${minutes}m`;
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Header */}
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Game Library</h1>
                <p className="text-muted-foreground">
                  {ownedGames.length} games • {formatPlaytime(totalPlaytime)} played
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === "grid" ? "steam" : "outline"}
                  size="icon"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "steam" : "outline"}
                  size="icon"
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search your library..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 bg-background border border-input rounded-md text-sm"
                >
                  <option value="recent">Recently Added</option>
                  <option value="name">Name</option>
                  <option value="playtime">Playtime</option>
                </select>
                
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Grid className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Games</p>
                    <p className="text-2xl font-bold">{ownedGames.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <Clock className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Playtime</p>
                    <p className="text-2xl font-bold">{formatPlaytime(totalPlaytime)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <Trophy className="h-5 w-5 text-yellow-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Achievements</p>
                    <p className="text-2xl font-bold">12</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Star className="h-5 w-5 text-purple-500" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Favorites</p>
                    <p className="text-2xl font-bold">{favorites.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Games Tabs */}
          <Tabs defaultValue="all" className="space-y-6">
            <TabsList>
              <TabsTrigger value="all">All Games ({ownedGames.length})</TabsTrigger>
              <TabsTrigger value="recent">Recently Played ({recentlyPlayed.length})</TabsTrigger>
              <TabsTrigger value="favorites">Favorites ({favorites.length})</TabsTrigger>
            </TabsList>

            {/* All Games */}
            <TabsContent value="all">
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-64 bg-muted animate-pulse rounded-lg" />
                  ))}
                </div>
              ) : sortedGames.length > 0 ? (
                <div className={
                  viewMode === "grid" 
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    : "space-y-4"
                }>
                  {sortedGames.map((game) => (
                    <div key={game._id}>
                      {viewMode === "grid" ? (
                        <GameCard game={game} />
                      ) : (
                        <Card className="hover:bg-muted/50 transition-colors">
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-4">
                              <img
                                src={game.images.thumbnail}
                                alt={game.title}
                                className="h-16 w-16 rounded object-cover"
                              />
                              <div className="flex-1">
                                <h3 className="font-semibold">{game.title}</h3>
                                <p className="text-sm text-muted-foreground">
                                  {game.genre.join(", ")}
                                </p>
                                <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                                  <span>Last played: 2 hours ago</span>
                                  <span>Playtime: 5.2h</span>
                                </div>
                              </div>
                              <Button variant="steam" size="sm" className="btn-glow">
                                Play Now
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Grid className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No games found</h3>
                    <p className="text-muted-foreground mb-4">
                      Try adjusting your search or browse the store for new games.
                    </p>
                    <Button variant="steam" className="btn-glow">
                      Browse Store
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Recently Played */}
            <TabsContent value="recent">
              {recentlyPlayed.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {recentlyPlayed.map((game) => (
                    <GameCard key={game._id} game={game} />
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No recent games</h3>
                    <p className="text-muted-foreground">
                      Play some games to see them here.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Favorites */}
            <TabsContent value="favorites">
              {favorites.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {favorites.map((game) => (
                    <GameCard key={game._id} game={game} />
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No favorite games</h3>
                    <p className="text-muted-foreground">
                      Add games to your favorites to see them here.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}