import { mutation } from "./_generated/server";

/**
 * Unified seed function that populates the database with comprehensive demo data
 * This creates a complete gaming platform experience with:
 * - Games catalog
 * - Demo users with realistic stats
 * - Active game sessions
 * - User libraries and reviews
 * - Achievements system
 * - Friend relationships
 * - Chat messages
 * - Notifications
 */
export const seedAll = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if database is already seeded
    const existingGames = await ctx.db.query("games").collect();
    const existingUsers = await ctx.db.query("users").collect();

    if (existingGames.length > 0 || existingUsers.length > 0) {
      return {
        success: false,
        message: "Database already contains data. Use clear_seed.clearAll first if you want to re-seed.",
        counts: {
          games: existingGames.length,
          users: existingUsers.length
        }
      };
    }

    const results = {
      games: 0,
      users: 0,
      userLibrary: 0,
      gameSessions: 0,
      achievements: 0,
      userAchievements: 0,
      gameReviews: 0,
      friends: 0,
      chatMessages: 0,
      notifications: 0,
      wishlists: 0
    };

    try {
      // 1. SEED GAMES
      const games = [
        {
          title: "Texas Hold'em Poker",
          description: "The classic poker variant that's easy to learn but hard to master. Play with friends or strangers in tournaments and cash games. Features include blinds, all-in betting, and showdown mechanics.",
          shortDescription: "Classic poker for 2-9 players",
          genre: ["Card Game", "Strategy", "Multiplayer", "Casino"],
          gameType: "poker" as const,
          images: {
            header: "/api/placeholder/400/225",
            screenshots: [
              "/api/placeholder/800/450",
              "/api/placeholder/800/450",
              "/api/placeholder/800/450"
            ],
            thumbnail: "/api/placeholder/200/200"
          },
          price: 0,
          rating: 4.5,
          reviewCount: 1234,
          developer: "GameHub Studios",
          publisher: "GameHub",
          releaseDate: Date.now() - ******** * 30, // 30 days ago
          minPlayers: 2,
          maxPlayers: 9,
          estimatedPlayTime: 45,
          rules: {
            description: "Each player is dealt two hole cards. Five community cards are dealt in three stages: the flop (3 cards), the turn (1 card), and the river (1 card). Players must make the best 5-card hand using any combination of their hole cards and the community cards.",
            objective: "Win chips by making the best hand or by forcing all other players to fold",
            setup: "Each player starts with an equal number of chips. Two players post small and big blinds to start the action."
          },
          featured: true,
          isActive: true,
        },
        {
          title: "Blackjack",
          description: "Beat the dealer in this classic casino card game. Master basic strategy and card counting to maximize your wins. Features multiple decks, splitting, doubling down, and insurance bets.",
          shortDescription: "Classic casino card game",
          genre: ["Card Game", "Casino", "Strategy"],
          gameType: "blackjack" as const,
          images: {
            header: "/api/placeholder/400/225",
            screenshots: [
              "/api/placeholder/800/450",
              "/api/placeholder/800/450"
            ],
            thumbnail: "/api/placeholder/200/200"
          },
          price: 0,
          rating: 4.2,
          reviewCount: 856,
          developer: "GameHub Studios",
          publisher: "GameHub",
          releaseDate: Date.now() - ******** * 45, // 45 days ago
          minPlayers: 1,
          maxPlayers: 7,
          estimatedPlayTime: 15,
          rules: {
            description: "Players compete against the dealer to get as close to 21 as possible without going over. Face cards are worth 10, Aces are worth 1 or 11.",
            objective: "Beat the dealer by getting closer to 21 without busting",
            setup: "Each player and the dealer start with 2 cards. One dealer card is face down."
          },
          featured: true,
          isActive: true,
        },
        {
          title: "UNO",
          description: "The beloved family card game that's easy to learn and fun for all ages. Match colors and numbers, use action cards, and don't forget to call UNO when you have one card left!",
          shortDescription: "Classic family card game",
          genre: ["Card Game", "Family", "Casual", "Multiplayer"],
          gameType: "uno" as const,
          images: {
            header: "/api/placeholder/400/225",
            screenshots: [
              "/api/placeholder/800/450",
              "/api/placeholder/800/450"
            ],
            thumbnail: "/api/placeholder/200/200"
          },
          price: 0,
          rating: 4.7,
          reviewCount: 2341,
          developer: "GameHub Studios",
          publisher: "GameHub",
          releaseDate: Date.now() - ******** * 60, // 60 days ago
          minPlayers: 2,
          maxPlayers: 8,
          estimatedPlayTime: 20,
          rules: {
            description: "Players take turns matching a card from their hand to the top card of the discard pile by color, number, or action. Special action cards include Skip, Reverse, Draw Two, Wild, and Wild Draw Four.",
            objective: "Be the first player to get rid of all your cards",
            setup: "Each player starts with 7 cards. The remaining cards form a draw pile."
          },
          featured: false,
          isActive: true,
        },
        {
          title: "Go Fish",
          description: "A simple and fun card game perfect for beginners. Collect sets of four cards by asking other players for cards of specific ranks. Great for learning and casual play.",
          shortDescription: "Simple collecting card game",
          genre: ["Card Game", "Family", "Casual"],
          gameType: "gofish" as const,
          images: {
            header: "/api/placeholder/400/225",
            screenshots: [
              "/api/placeholder/800/450"
            ],
            thumbnail: "/api/placeholder/200/200"
          },
          price: 0,
          rating: 4.0,
          reviewCount: 543,
          developer: "GameHub Studios",
          publisher: "GameHub",
          releaseDate: Date.now() - ******** * 15, // 15 days ago
          minPlayers: 2,
          maxPlayers: 6,
          estimatedPlayTime: 10,
          rules: {
            description: "Players try to collect sets of four cards of the same rank by asking other players for cards. If a player doesn't have the requested rank, they say 'Go Fish' and the asking player draws from the deck.",
            objective: "Collect the most sets of four cards",
            setup: "Each player starts with 7 cards (or 5 cards if more than 4 players). Remaining cards form the draw pile."
          },
          featured: false,
          isActive: true,
        }
      ];

      const gameIds = [];
      for (const game of games) {
        const id = await ctx.db.insert("games", game);
        gameIds.push(id);
      }
      results.games = gameIds.length;

      // 2. SEED USERS
      const users = [
        {
          email: "<EMAIL>",
          username: "PokerPro",
          displayName: "Alex the Poker Pro",
          avatar: "/api/placeholder/128/128",
          bio: "Professional poker player with 5+ years experience. Always up for a challenge!",
          joinDate: Date.now() - ******** * 180, // 6 months ago
          isOnline: true,
          lastActive: Date.now(),
          gamesPlayed: 234,
          gamesWon: 156,
          totalPlaytime: 15680, // minutes
          preferences: {
            notifications: true,
            soundEnabled: true,
            autoJoinGames: false,
            theme: "dark",
          },
          credits: 2500,
        },
        {
          email: "<EMAIL>",
          username: "CardShark",
          displayName: "Sarah",
          avatar: "/api/placeholder/128/128",
          bio: "Love playing card games with friends. Blackjack is my specialty!",
          joinDate: Date.now() - ******** * 120, // 4 months ago
          isOnline: true,
          lastActive: Date.now() - 300000, // 5 minutes ago
          gamesPlayed: 189,
          gamesWon: 98,
          totalPlaytime: 8940,
          preferences: {
            notifications: true,
            soundEnabled: true,
            autoJoinGames: false,
            theme: "dark",
          },
          credits: 1800,
        },
        {
          email: "<EMAIL>",
          username: "UnoChamp",
          displayName: "Mike Johnson",
          avatar: "/api/placeholder/128/128",
          bio: "Casual gamer who loves UNO and Go Fish. Always ready for a fun game!",
          joinDate: Date.now() - ******** * 60, // 2 months ago
          isOnline: false,
          lastActive: Date.now() - 3600000, // 1 hour ago
          gamesPlayed: 67,
          gamesWon: 34,
          totalPlaytime: 2340,
          preferences: {
            notifications: true,
            soundEnabled: false,
            autoJoinGames: true,
            theme: "dark",
          },
          credits: 950,
        },
        {
          email: "<EMAIL>",
          username: "LuckyPlayer",
          displayName: "Emma Wilson",
          avatar: "/api/placeholder/128/128",
          bio: "New to card games but learning fast. Looking for friendly matches!",
          joinDate: Date.now() - ******** * 30, // 1 month ago
          isOnline: true,
          lastActive: Date.now() - 600000, // 10 minutes ago
          gamesPlayed: 45,
          gamesWon: 18,
          totalPlaytime: 1230,
          preferences: {
            notifications: true,
            soundEnabled: true,
            autoJoinGames: false,
            theme: "dark",
          },
          credits: 1200,
        },
        {
          email: "<EMAIL>",
          username: "DemoUser",
          displayName: "Demo Player",
          avatar: "/api/placeholder/128/128",
          bio: "Demo account for testing the platform. Feel free to play games with me!",
          joinDate: Date.now() - ******** * 7, // 1 week ago
          isOnline: true,
          lastActive: Date.now(),
          gamesPlayed: 25,
          gamesWon: 12,
          totalPlaytime: 890,
          preferences: {
            notifications: true,
            soundEnabled: true,
            autoJoinGames: false,
            theme: "dark",
          },
          credits: 1000,
        }
      ];

      const userIds = [];
      for (const user of users) {
        const id = await ctx.db.insert("users", user);
        userIds.push(id);
      }
      results.users = userIds.length;

      // 3. SEED USER LIBRARIES (add games to user libraries)
      const userLibraryEntries = [
        // Alex owns all games
        { userId: userIds[0], gameId: gameIds[0], purchaseDate: Date.now() - ******** * 25, playtime: 2340, lastPlayed: Date.now() - ******** * 2, achievements: ["first_win", "poker_master"], personalRating: 5, hasReviewed: true },
        { userId: userIds[0], gameId: gameIds[1], purchaseDate: Date.now() - ******** * 20, playtime: 1890, lastPlayed: Date.now() - ******** * 1, achievements: ["blackjack_21"], personalRating: 4, hasReviewed: true },
        { userId: userIds[0], gameId: gameIds[2], purchaseDate: Date.now() - ******** * 15, playtime: 560, lastPlayed: Date.now() - ******** * 5, achievements: [], personalRating: 4, hasReviewed: false },

        // Sarah owns poker and blackjack
        { userId: userIds[1], gameId: gameIds[0], purchaseDate: Date.now() - ******** * 30, playtime: 1560, lastPlayed: Date.now() - ******** * 3, achievements: ["first_win"], personalRating: 5, hasReviewed: true },
        { userId: userIds[1], gameId: gameIds[1], purchaseDate: Date.now() - ******** * 35, playtime: 2890, lastPlayed: Date.now() - 300000, achievements: ["blackjack_21", "card_counter"], personalRating: 5, hasReviewed: true },

        // Mike owns UNO and Go Fish
        { userId: userIds[2], gameId: gameIds[2], purchaseDate: Date.now() - ******** * 40, playtime: 1230, lastPlayed: Date.now() - ******** * 7, achievements: ["uno_champion"], personalRating: 5, hasReviewed: true },
        { userId: userIds[2], gameId: gameIds[3], purchaseDate: Date.now() - ******** * 10, playtime: 890, lastPlayed: Date.now() - ******** * 4, achievements: ["go_fish_master"], personalRating: 4, hasReviewed: false },

        // Emma owns all games (new player exploring)
        { userId: userIds[3], gameId: gameIds[0], purchaseDate: Date.now() - ******** * 20, playtime: 340, lastPlayed: Date.now() - ******** * 8, achievements: [], personalRating: 3, hasReviewed: false },
        { userId: userIds[3], gameId: gameIds[1], purchaseDate: Date.now() - ******** * 18, playtime: 290, lastPlayed: Date.now() - ******** * 6, achievements: [], personalRating: 4, hasReviewed: false },
        { userId: userIds[3], gameId: gameIds[2], purchaseDate: Date.now() - ******** * 15, playtime: 450, lastPlayed: Date.now() - 600000, achievements: ["first_win"], personalRating: 5, hasReviewed: true },
        { userId: userIds[3], gameId: gameIds[3], purchaseDate: Date.now() - ******** * 12, playtime: 150, lastPlayed: Date.now() - ******** * 10, achievements: [], personalRating: 4, hasReviewed: false },

        // Demo user owns poker and UNO
        { userId: userIds[4], gameId: gameIds[0], purchaseDate: Date.now() - ******** * 5, playtime: 560, lastPlayed: Date.now() - ******** * 1, achievements: ["first_win"], personalRating: 4, hasReviewed: false },
        { userId: userIds[4], gameId: gameIds[2], purchaseDate: Date.now() - ******** * 3, playtime: 330, lastPlayed: Date.now() - ******** * 2, achievements: [], personalRating: 5, hasReviewed: false },
      ];

      for (const entry of userLibraryEntries) {
        await ctx.db.insert("userLibrary", entry);
      }
      results.userLibrary = userLibraryEntries.length;

      // 4. SEED GAME SESSIONS
      const sessions = [
        {
          gameId: gameIds[0], // Poker
          hostId: userIds[0],
          players: [
            {
              userId: userIds[0],
              username: users[0].username,
              avatar: users[0].avatar,
              joinedAt: Date.now() - 600000, // 10 minutes ago
              isReady: true,
              position: 0,
            },
            {
              userId: userIds[1],
              username: users[1].username,
              avatar: users[1].avatar,
              joinedAt: Date.now() - 300000, // 5 minutes ago
              isReady: true,
              position: 1,
            }
          ],
          spectators: [],
          status: "waiting" as const,
          currentTurn: undefined,
          turnStartTime: undefined,
          turnTimeLimit: 60000, // 60 seconds
          gameState: undefined,
          startTime: undefined,
          endTime: undefined,
          winner: undefined,
          isPrivate: false,
          password: undefined,
          maxPlayers: games[0].maxPlayers,
          settings: {
            allowSpectators: true,
            autoStart: false,
            difficulty: undefined,
          },
        },
        {
          gameId: gameIds[1], // Blackjack
          hostId: userIds[1],
          players: [
            {
              userId: userIds[1],
              username: users[1].username,
              avatar: users[1].avatar,
              joinedAt: Date.now() - 900000, // 15 minutes ago
              isReady: true,
              position: 0,
            }
          ],
          spectators: [],
          status: "waiting" as const,
          currentTurn: undefined,
          turnStartTime: undefined,
          turnTimeLimit: 30000, // 30 seconds
          gameState: undefined,
          startTime: undefined,
          endTime: undefined,
          winner: undefined,
          isPrivate: false,
          password: undefined,
          maxPlayers: games[1].maxPlayers,
          settings: {
            allowSpectators: true,
            autoStart: true,
            difficulty: "medium",
          },
        }
      ];

      for (const session of sessions) {
        await ctx.db.insert("gameSessions", session);
      }
      results.gameSessions = sessions.length;

      // 5. SEED ACHIEVEMENTS
      const achievements = [
        {
          gameId: gameIds[0], // Poker
          name: "First Win",
          description: "Win your first poker game",
          icon: "🏆",
          type: "progression" as const,
          requirements: { description: "Win 1 poker game", target: 1, metric: "poker_wins" },
          rarity: "common" as const,
          points: 10,
          isActive: true,
        },
        {
          gameId: gameIds[0], // Poker
          name: "Poker Master",
          description: "Win 50 poker games",
          icon: "👑",
          type: "skill" as const,
          requirements: { description: "Win 50 poker games", target: 50, metric: "poker_wins" },
          rarity: "rare" as const,
          points: 100,
          isActive: true,
        },
        {
          gameId: gameIds[1], // Blackjack
          name: "Blackjack 21",
          description: "Get a natural blackjack",
          icon: "🃏",
          type: "skill" as const,
          requirements: { description: "Get a natural 21", target: 1, metric: "natural_blackjack" },
          rarity: "uncommon" as const,
          points: 25,
          isActive: true,
        },
        {
          gameId: gameIds[2], // UNO
          name: "UNO Champion",
          description: "Win 10 UNO games",
          icon: "🎯",
          type: "progression" as const,
          requirements: { description: "Win 10 UNO games", target: 10, metric: "uno_wins" },
          rarity: "uncommon" as const,
          points: 50,
          isActive: true,
        },
        {
          gameId: undefined, // Global achievement
          name: "Social Butterfly",
          description: "Add 5 friends",
          icon: "👥",
          type: "social" as const,
          requirements: { description: "Add 5 friends", target: 5, metric: "friends_added" },
          rarity: "common" as const,
          points: 20,
          isActive: true,
        }
      ];

      const achievementIds = [];
      for (const achievement of achievements) {
        const id = await ctx.db.insert("achievements", achievement);
        achievementIds.push(id);
      }
      results.achievements = achievementIds.length;

      // 6. SEED GAME REVIEWS
      const reviews = [
        {
          userId: userIds[0], gameId: gameIds[0], username: users[0].username,
          rating: 5, review: "Amazing poker experience! The interface is smooth and the gameplay is fantastic.",
          helpful: 12, timestamp: Date.now() - ******** * 5, playtime: 2340, recommended: true,
        },
        {
          userId: userIds[1], gameId: gameIds[1], username: users[1].username,
          rating: 5, review: "Best blackjack game I've played online. Great for practicing card counting!",
          helpful: 8, timestamp: Date.now() - ******** * 3, playtime: 2890, recommended: true,
        },
        {
          userId: userIds[2], gameId: gameIds[2], username: users[2].username,
          rating: 5, review: "UNO is so much fun with friends! Easy to learn and addictive.",
          helpful: 15, timestamp: Date.now() - ******** * 7, playtime: 1230, recommended: true,
        },
        {
          userId: userIds[3], gameId: gameIds[2], username: users[3].username,
          rating: 4, review: "Great family game! Perfect for casual gaming sessions.",
          helpful: 6, timestamp: Date.now() - ******** * 2, playtime: 450, recommended: true,
        }
      ];

      for (const review of reviews) {
        await ctx.db.insert("gameReviews", review);
      }
      results.gameReviews = reviews.length;

      // 7. SEED FRIEND RELATIONSHIPS
      const friendships = [
        // Alex and Sarah are friends
        { userId: userIds[0], friendId: userIds[1], status: "accepted" as const, requestDate: Date.now() - ******** * 100, acceptDate: Date.now() - ******** * 99, requestedBy: userIds[0] },
        { userId: userIds[1], friendId: userIds[0], status: "accepted" as const, requestDate: Date.now() - ******** * 100, acceptDate: Date.now() - ******** * 99, requestedBy: userIds[0] },

        // Emma and Demo user are friends
        { userId: userIds[3], friendId: userIds[4], status: "accepted" as const, requestDate: Date.now() - ******** * 5, acceptDate: Date.now() - ******** * 4, requestedBy: userIds[3] },
        { userId: userIds[4], friendId: userIds[3], status: "accepted" as const, requestDate: Date.now() - ******** * 5, acceptDate: Date.now() - ******** * 4, requestedBy: userIds[3] },

        // Mike has pending request to Alex
        { userId: userIds[2], friendId: userIds[0], status: "pending" as const, requestDate: Date.now() - ******** * 2, acceptDate: undefined, requestedBy: userIds[2] },
      ];

      for (const friendship of friendships) {
        await ctx.db.insert("friends", friendship);
      }
      results.friends = friendships.length;

      // 8. SEED CHAT MESSAGES
      const messages = [
        {
          senderId: userIds[0], senderUsername: users[0].username, recipientId: undefined, gameSessionId: undefined,
          message: "Welcome to GameHub! Ready for some poker?", timestamp: Date.now() - ******** * 1,
          type: "global" as const, edited: false, editedAt: undefined,
        },
        {
          senderId: userIds[1], senderUsername: users[1].username, recipientId: undefined, gameSessionId: undefined,
          message: "Anyone up for a blackjack game?", timestamp: Date.now() - 3600000,
          type: "global" as const, edited: false, editedAt: undefined,
        },
        {
          senderId: userIds[4], senderUsername: users[4].username, recipientId: undefined, gameSessionId: undefined,
          message: "New to the platform, looking forward to playing with everyone!", timestamp: Date.now() - 1800000,
          type: "global" as const, edited: false, editedAt: undefined,
        }
      ];

      for (const message of messages) {
        await ctx.db.insert("chatMessages", message);
      }
      results.chatMessages = messages.length;

      // 9. SEED NOTIFICATIONS
      const notifications = [
        {
          userId: userIds[0], type: "friend_request" as const,
          title: "New Friend Request", message: "UnoChamp wants to be your friend",
          data: JSON.stringify({ fromUserId: userIds[2] }), isRead: false,
          timestamp: Date.now() - ******** * 2, expiresAt: undefined,
        },
        {
          userId: userIds[3], type: "achievement_unlocked" as const,
          title: "Achievement Unlocked!", message: "You earned the 'First Win' achievement",
          data: JSON.stringify({ achievementId: achievementIds[0] }), isRead: true,
          timestamp: Date.now() - ******** * 1, expiresAt: undefined,
        }
      ];

      for (const notification of notifications) {
        await ctx.db.insert("notifications", notification);
      }
      results.notifications = notifications.length;

      // 10. SEED WISHLISTS
      const wishlists = [
        { userId: userIds[2], gameId: gameIds[0], addedAt: Date.now() - ******** * 10, notifyOnSale: true }, // Mike wants poker
        { userId: userIds[3], gameId: gameIds[3], addedAt: Date.now() - ******** * 5, notifyOnSale: false }, // Emma wants Go Fish
      ];

      for (const wishlist of wishlists) {
        await ctx.db.insert("wishlists", wishlist);
      }
      results.wishlists = wishlists.length;

      return {
        success: true,
        message: "Database successfully seeded with comprehensive demo data!",
        results,
        summary: `Created ${results.games} games, ${results.users} users, ${results.userLibrary} library entries, ${results.gameSessions} sessions, ${results.achievements} achievements, ${results.gameReviews} reviews, ${results.friends} friendships, ${results.chatMessages} messages, ${results.notifications} notifications, and ${results.wishlists} wishlist items.`
      };

    } catch (error) {
      return {
        success: false,
        message: `Seeding failed: ${error}`,
        results
      };
    }
  },
});