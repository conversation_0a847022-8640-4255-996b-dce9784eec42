"use client";

import { useAuthActions, useAuthToken } from "@convex-dev/auth/react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

// Hook to get current authenticated user
export function useAuth() {
  const { signOut } = useAuthActions();
  const token = useAuthToken();

  // Get current user from our auth system (already returns full user object)
  const currentUser = useQuery(api.auth.currentUser);

  return {
    user: currentUser,
    isAuthenticated: !!token,
    isLoading: token === undefined || (token && currentUser === undefined),
    signOut,
  };
}

// Hook specifically for authentication state
export function useAuthState() {
  const token = useAuthToken();
  const currentUser = useQuery(api.auth.currentUser);

  return {
    isAuthenticated: !!token,
    isLoading: token === undefined,
    user: currentUser,
  };
}

// Hook for getting current user data
export function useCurrentUserData() {
  const token = useAuthToken();
  const currentUser = useQuery(api.auth.currentUser);

  return {
    user: currentUser,
    isLoading: token === undefined || (token && currentUser === undefined),
    isAuthenticated: !!token,
  };
}