"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

export function useActiveSessions(options?: {
  gameType?: "poker" | "blackjack" | "uno" | "gofish";
  limit?: number;
}) {
  return useQuery(api.gameSessions.getActive, options || {});
}

export function useGameSession(id: string) {
  return useQuery(api.gameSessions.get, { id: id as any });
}

export function useSessionsByGame(gameId: string, options?: {
  status?: "waiting" | "active" | "finished" | "cancelled";
  limit?: number;
}) {
  return useQuery(api.gameSessions.getByGame, { 
    gameId: gameId as any, 
    ...options 
  });
}

export function useUserSessions(userId: string, options?: {
  status?: "waiting" | "active" | "finished" | "cancelled";
  limit?: number;
}) {
  return useQuery(api.gameSessions.getUserSessions, { 
    userId: userId as any, 
    ...options 
  });
}

export function useSessionStats() {
  return useQuery(api.gameSessions.getStats, {});
}

// Mutations
export function useCreateSession() {
  return useMutation(api.gameSessions.create);
}

export function useJoinSession() {
  return useMutation(api.gameSessions.join);
}

export function useLeaveSession() {
  return useMutation(api.gameSessions.leave);
}

export function useToggleReady() {
  return useMutation(api.gameSessions.toggleReady);
}

export function useStartSession() {
  return useMutation(api.gameSessions.start);
}

export function useEndSession() {
  return useMutation(api.gameSessions.end);
}