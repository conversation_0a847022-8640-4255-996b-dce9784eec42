import { v } from "convex/values";
import { query, mutation } from "./_generated/server";

// Get all games with optional filtering
export const list = query({
  args: {
    gameType: v.optional(v.union(v.literal("poker"), v.literal("blackjack"), v.literal("uno"), v.literal("gofish"))),
    featured: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let q = ctx.db.query("games");
    
    if (args.gameType) {
      q = q.withIndex("by_game_type", (q) => q.eq("gameType", args.gameType));
    }
    
    if (args.featured !== undefined) {
      q = q.filter((q) => q.eq(q.field("featured"), args.featured));
    }
    
    q = q.filter((q) => q.eq(q.field("isActive"), true));
    
    const limit = args.limit ?? 20;
    return await q.order("desc").take(limit);
  },
});

// Get featured games
export const getFeatured = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 6;
    return await ctx.db
      .query("games")
      .withIndex("by_featured", (q) => q.eq("featured", true))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(limit);
  },
});

// Get a single game by ID
export const get = query({
  args: { id: v.id("games") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Search games
export const search = query({
  args: {
    query: v.string(),
    gameType: v.optional(v.union(v.literal("poker"), v.literal("blackjack"), v.literal("uno"), v.literal("gofish"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    const query = args.query.toLowerCase();
    
    let games = await ctx.db.query("games").collect();
    
    // Filter by game type if specified
    if (args.gameType) {
      games = games.filter(game => game.gameType === args.gameType);
    }
    
    // Filter by search query
    games = games.filter(game => 
      game.isActive &&
      (game.title.toLowerCase().includes(query) ||
       game.description.toLowerCase().includes(query) ||
       game.genre.some(g => g.toLowerCase().includes(query)))
    );
    
    return games.slice(0, limit);
  },
});

// Get games by genre
export const getByGenre = query({
  args: {
    genre: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    const games = await ctx.db.query("games").collect();
    
    return games
      .filter(game => 
        game.isActive &&
        game.genre.some(g => g.toLowerCase() === args.genre.toLowerCase())
      )
      .slice(0, limit);
  },
});

// Create a new game (admin function)
export const create = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    shortDescription: v.string(),
    genre: v.array(v.string()),
    gameType: v.union(v.literal("poker"), v.literal("blackjack"), v.literal("uno"), v.literal("gofish")),
    images: v.object({
      header: v.string(),
      screenshots: v.array(v.string()),
      thumbnail: v.string(),
    }),
    price: v.number(),
    developer: v.string(),
    publisher: v.string(),
    minPlayers: v.number(),
    maxPlayers: v.number(),
    estimatedPlayTime: v.number(),
    rules: v.object({
      description: v.string(),
      objective: v.string(),
      setup: v.string(),
    }),
    featured: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("games", {
      ...args,
      rating: 0,
      reviewCount: 0,
      releaseDate: Date.now(),
      featured: args.featured ?? false,
      isActive: true,
    });
  },
});

// Update game rating
export const updateRating = mutation({
  args: {
    gameId: v.id("games"),
    newRating: v.number(),
    reviewCount: v.number(),
  },
  handler: async (ctx, args) => {
    const game = await ctx.db.get(args.gameId);
    if (!game) {
      throw new Error("Game not found");
    }

    await ctx.db.patch(args.gameId, {
      rating: args.newRating,
      reviewCount: args.reviewCount,
    });

    return args.gameId;
  },
});

// Toggle featured status
export const toggleFeatured = mutation({
  args: { gameId: v.id("games") },
  handler: async (ctx, args) => {
    const game = await ctx.db.get(args.gameId);
    if (!game) {
      throw new Error("Game not found");
    }

    await ctx.db.patch(args.gameId, {
      featured: !game.featured,
    });

    return args.gameId;
  },
});

// Get game statistics
export const getStats = query({
  args: { gameId: v.id("games") },
  handler: async (ctx, args) => {
    const game = await ctx.db.get(args.gameId);
    if (!game) {
      return null;
    }

    // Get active sessions for this game
    const activeSessions = await ctx.db
      .query("gameSessions")
      .withIndex("by_game", (q) => q.eq("gameId", args.gameId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    // Get total players currently playing
    const totalActivePlayers = activeSessions.reduce(
      (sum, session) => sum + (session.players?.length || 0),
      0
    );

    // Get all-time sessions
    const allSessions = await ctx.db
      .query("gameSessions")
      .withIndex("by_game", (q) => q.eq("gameId", args.gameId))
      .collect();

    const completedSessions = allSessions.filter(s => s.status === "finished");
    const totalGamesPlayed = completedSessions.length;

    return {
      activeSessions: activeSessions.length,
      totalActivePlayers,
      totalGamesPlayed,
      averageSessionLength: totalGamesPlayed > 0 
        ? completedSessions.reduce((sum, session) => {
            const duration = (session.endTime || 0) - (session.startTime || 0);
            return sum + duration;
          }, 0) / totalGamesPlayed
        : 0,
    };
  },
});