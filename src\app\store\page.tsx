"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { GameCard } from "@/components/game/game-card";
import { Search, Filter, SortAsc, Grid, List } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useFeaturedGames, useGames } from "@/hooks/useGames";

export default function StorePage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedGenre, setSelectedGenre] = useState<string>("all");
  const [selectedGameType, setSelectedGameType] = useState<"all" | "poker" | "blackjack" | "uno" | "gofish">("all");
  const [sortBy, setSortBy] = useState<"title" | "rating" | "price" | "releaseDate">("title");

  // Use real data from Convex
  const featuredGames = useFeaturedGames(6);
  const allGames = useGames({
    gameType: selectedGameType === "all" ? undefined : selectedGameType,
    limit: 50
  });

  const genres = [
    "All",
    "Card Game",
    "Strategy", 
    "Casino",
    "Family",
    "Multiplayer",
    "Casual"
  ];

  const gameTypes = [
    { value: "all", label: "All Game Types" },
    { value: "poker", label: "Poker" },
    { value: "blackjack", label: "Blackjack" },
    { value: "uno", label: "UNO" },
    { value: "gofish", label: "Go Fish" }
  ];

  const sortOptions = [
    { value: "title", label: "Name A-Z" },
    { value: "rating", label: "Highest Rated" },
    { value: "price", label: "Price: Low to High" },
    { value: "releaseDate", label: "Release Date" }
  ];

  // Filter and sort games
  const filteredGames = allGames?.filter((game) => {
    const matchesSearch = searchQuery === "" || 
      game.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      game.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesGenre = selectedGenre === "all" || 
      game.genre.some(g => g.toLowerCase() === selectedGenre.toLowerCase());

    return matchesSearch && matchesGenre;
  }) || [];

  const sortedGames = [...filteredGames].sort((a, b) => {
    switch (sortBy) {
      case "title":
        return a.title.localeCompare(b.title);
      case "rating":
        return (b.rating || 0) - (a.rating || 0);
      case "price":
        return a.price - b.price;
      case "releaseDate":
        return (b.releaseDate || 0) - (a.releaseDate || 0);
      default:
        return 0;
    }
  });

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold">Game Store</h1>
              <p className="text-muted-foreground">
                Discover and play amazing card games with friends
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search games..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex gap-2">
              {/* Genre Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    {selectedGenre === "all" ? "All Genres" : selectedGenre}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Genre</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {genres.map((genre) => (
                    <DropdownMenuItem
                      key={genre}
                      onClick={() => setSelectedGenre(genre.toLowerCase())}
                    >
                      {genre}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Game Type Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    {gameTypes.find(gt => gt.value === selectedGameType)?.label}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Game Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {gameTypes.map((type) => (
                    <DropdownMenuItem
                      key={type.value}
                      onClick={() => setSelectedGameType(type.value as any)}
                    >
                      {type.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Sort */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <SortAsc className="mr-2 h-4 w-4" />
                    Sort
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {sortOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onClick={() => setSortBy(option.value as any)}
                    >
                      {option.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Featured Games */}
        {featuredGames && featuredGames.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">Featured Games</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredGames.slice(0, 3).map((game) => (
                <GameCard key={game._id} game={game} />
              ))}
            </div>
          </div>
        )}

        {/* All Games */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">
              All Games ({sortedGames.length})
            </h2>
            {searchQuery && (
              <p className="text-muted-foreground">
                Results for "{searchQuery}"
              </p>
            )}
          </div>

          {sortedGames.length === 0 ? (
            <Card>
              <CardHeader className="text-center">
                <CardTitle>No games found</CardTitle>
                <CardDescription>
                  Try adjusting your search criteria or browse all games
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button 
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedGenre("all");
                    setSelectedGameType("all");
                  }}
                >
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className={viewMode === "grid" 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              : "space-y-4"
            }>
              {sortedGames.map((game) => (
                <GameCard 
                  key={game._id} 
                  game={game} 
                  className={viewMode === "list" ? "flex flex-row" : undefined}
                />
              ))}
            </div>
          )}
        </div>

        {/* Load More */}
        {sortedGames.length > 0 && sortedGames.length % 20 === 0 && (
          <div className="text-center">
            <Button variant="outline" size="lg">
              Load More Games
            </Button>
          </div>
        )}
      </div>
    </MainLayout>
  );
}