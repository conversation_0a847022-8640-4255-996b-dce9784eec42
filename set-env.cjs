const fs = require('fs');
const { execSync } = require('child_process');

// Read the private key
const privateKey = fs.readFileSync('private_key.pem', 'utf8');
const publicKey = fs.readFileSync('public_key.pem', 'utf8');

console.log('Setting JWT_PRIVATE_KEY...');
try {
  // Convert to base64 to avoid command line parsing issues
  const base64Key = Buffer.from(privateKey).toString('base64');

  // Use execSync to set the environment variable with base64 encoded key
  execSync(`npx convex env set JWT_PRIVATE_KEY_TEMP "${base64Key}"`, {
    stdio: 'inherit',
    shell: true
  });

  console.log('✅ JWT_PRIVATE_KEY_TEMP set successfully (base64 encoded)');
  console.log('⚠️  You need to decode this in your Convex functions or set it manually in the dashboard');
} catch (error) {
  console.error('❌ Failed to set JWT_PRIVATE_KEY_TEMP:', error.message);
}

console.log('\n📋 Public key for Convex configuration:');
console.log(publicKey);
console.log('\n💡 You may need to configure this public key in your Convex dashboard for JWT verification.');

console.log('\n🔑 Base64 encoded private key:');
console.log(Buffer.from(privateKey).toString('base64'));
