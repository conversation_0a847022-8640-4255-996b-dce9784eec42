"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { useAuth } from "@/hooks/useAuth";
import { User, Settings, Trophy, Clock, Star, Upload, Save } from "lucide-react";

export default function ProfilePage() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState(user?.displayName || "");
  const [bio, setBio] = useState(user?.bio || "");
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement profile update mutation
      console.log("Saving profile:", { displayName, bio });
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to save profile:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const formatPlaytime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${remainingMinutes}m`;
  };

  const winRate = user?.gamesPlayed ? 
    Math.round((user.gamesWon / user.gamesPlayed) * 100) : 0;

  if (!user) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="container mx-auto px-4 py-8">
            <div className="text-center">
              <p>Loading profile...</p>
            </div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Profile Header */}
          <div className="flex flex-col md:flex-row items-start gap-6">
            <div className="relative">
              <Avatar className="h-32 w-32">
                <AvatarImage src={user.avatar} alt={user.displayName} />
                <AvatarFallback className="text-2xl">
                  {user.displayName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {isEditing && (
                <Button
                  size="icon"
                  variant="outline"
                  className="absolute -bottom-2 -right-2"
                >
                  <Upload className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex-1 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold">{user.displayName}</h1>
                  <p className="text-muted-foreground">@{user.username}</p>
                </div>
                <Button
                  variant={isEditing ? "outline" : "steam"}
                  onClick={() => isEditing ? handleSave() : setIsEditing(true)}
                  disabled={isSaving}
                  className="btn-glow"
                >
                  {isEditing ? (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {isSaving ? "Saving..." : "Save"}
                    </>
                  ) : (
                    <>
                      <Settings className="mr-2 h-4 w-4" />
                      Edit Profile
                    </>
                  )}
                </Button>
              </div>

              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={displayName}
                      onChange={(e) => setDisplayName(e.target.value)}
                      className="max-w-md"
                    />
                  </div>
                  <div>
                    <Label htmlFor="bio">Bio</Label>
                    <Input
                      id="bio"
                      value={bio}
                      onChange={(e) => setBio(e.target.value)}
                      placeholder="Tell others about yourself..."
                      className="max-w-md"
                    />
                  </div>
                </div>
              ) : (
                <div>
                  <p className="text-muted-foreground">
                    {user.bio || "No bio provided"}
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Member since {new Date(user.joinDate).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Trophy className="h-8 w-8 text-yellow-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Games Won</p>
                    <p className="text-2xl font-bold">{user.gamesWon}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <User className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Games Played</p>
                    <p className="text-2xl font-bold">{user.gamesPlayed}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Star className="h-8 w-8 text-green-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Win Rate</p>
                    <p className="text-2xl font-bold">{winRate}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="h-8 w-8 text-purple-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Playtime</p>
                    <p className="text-2xl font-bold">{formatPlaytime(user.totalPlaytime)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="activity" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="activity">Recent Activity</TabsTrigger>
              <TabsTrigger value="achievements">Achievements</TabsTrigger>
              <TabsTrigger value="friends">Friends</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            {/* Recent Activity */}
            <TabsContent value="activity" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>
                    Your recent gaming activity and achievements
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="h-10 w-10 bg-green-500/20 rounded-full flex items-center justify-center">
                        <Trophy className="h-5 w-5 text-green-500" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Won a game of Texas Hold'em</p>
                        <p className="text-sm text-muted-foreground">2 hours ago</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="h-10 w-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-500" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Joined multiplayer lobby</p>
                        <p className="text-sm text-muted-foreground">5 hours ago</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 p-4 border rounded-lg">
                      <div className="h-10 w-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <Star className="h-5 w-5 text-purple-500" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">Achieved 10 game wins milestone</p>
                        <p className="text-sm text-muted-foreground">1 day ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Achievements */}
            <TabsContent value="achievements" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Achievements</CardTitle>
                  <CardDescription>
                    Unlock achievements by playing games and completing challenges
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-3 mb-2">
                        <Trophy className="h-6 w-6 text-yellow-500" />
                        <h4 className="font-semibold">First Win</h4>
                      </div>
                      <p className="text-sm text-muted-foreground">Win your first game</p>
                      <p className="text-xs text-green-500 mt-1">Unlocked</p>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-3 mb-2">
                        <Star className="h-6 w-6 text-blue-500" />
                        <h4 className="font-semibold">Social Player</h4>
                      </div>
                      <p className="text-sm text-muted-foreground">Add 5 friends</p>
                      <p className="text-xs text-muted-foreground mt-1">3/5</p>
                    </div>

                    <div className="p-4 border rounded-lg opacity-50">
                      <div className="flex items-center space-x-3 mb-2">
                        <Clock className="h-6 w-6 text-gray-500" />
                        <h4 className="font-semibold">Marathon Player</h4>
                      </div>
                      <p className="text-sm text-muted-foreground">Play for 10 hours total</p>
                      <p className="text-xs text-muted-foreground mt-1">Locked</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Friends */}
            <TabsContent value="friends" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Friends</CardTitle>
                  <CardDescription>
                    Manage your friends and social connections
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Friend management will be available soon.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings */}
            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>User Preferences</CardTitle>
                  <CardDescription>
                    Customize your gaming experience
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications for game invites and messages
                      </p>
                    </div>
                    <input 
                      type="checkbox" 
                      defaultChecked={user.preferences?.notifications}
                      className="toggle"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Sound Effects</Label>
                      <p className="text-sm text-muted-foreground">
                        Play sound effects during games
                      </p>
                    </div>
                    <input 
                      type="checkbox" 
                      defaultChecked={user.preferences?.soundEnabled}
                      className="toggle"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Auto-join Games</Label>
                      <p className="text-sm text-muted-foreground">
                        Automatically join available games when invited
                      </p>
                    </div>
                    <input 
                      type="checkbox" 
                      defaultChecked={user.preferences?.autoJoinGames}
                      className="toggle"
                    />
                  </div>

                  <Button variant="steam" className="btn-glow">
                    Save Preferences
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}