"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/useAuth";
import { 
  Search, 
  Users, 
  Plus, 
  Clock, 
  Star, 
  Filter,
  Gamepad2,
  Crown,
  Eye,
  Play
} from "lucide-react";

export default function LobbiesPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGameType, setSelectedGameType] = useState<string>("all");
  const [showCreateLobby, setShowCreateLobby] = useState(false);

  // Mock lobby data - will be replaced with real data
  const mockLobbies = [
    {
      id: "1",
      name: "Friday Night Poker",
      gameType: "poker",
      host: { name: "PokerPro", avatar: null, isOnline: true },
      players: [
        { name: "PokerPro", avatar: null },
        { name: "CardShark", avatar: null },
        { name: "BluffMaster", avatar: null }
      ],
      maxPlayers: 6,
      currentPlayers: 3,
      isPrivate: false,
      status: "waiting",
      stakes: "Medium",
      createdAt: new Date(Date.now() - 1000 * 60 * 15) // 15 minutes ago
    },
    {
      id: "2", 
      name: "Quick Blackjack",
      gameType: "blackjack",
      host: { name: "DealerDan", avatar: null, isOnline: true },
      players: [
        { name: "DealerDan", avatar: null },
        { name: "LuckyLuke", avatar: null }
      ],
      maxPlayers: 4,
      currentPlayers: 2,
      isPrivate: false,
      status: "in-progress",
      stakes: "Low",
      createdAt: new Date(Date.now() - 1000 * 60 * 5) // 5 minutes ago
    },
    {
      id: "3",
      name: "UNO Championship",
      gameType: "uno",
      host: { name: "UnoMaster", avatar: null, isOnline: true },
      players: [
        { name: "UnoMaster", avatar: null }
      ],
      maxPlayers: 8,
      currentPlayers: 1,
      isPrivate: false,
      status: "waiting",
      stakes: "High",
      createdAt: new Date(Date.now() - 1000 * 60 * 2) // 2 minutes ago
    }
  ];

  const gameTypes = [
    { value: "all", label: "All Games", count: mockLobbies.length },
    { value: "poker", label: "Poker", count: mockLobbies.filter(l => l.gameType === "poker").length },
    { value: "blackjack", label: "Blackjack", count: mockLobbies.filter(l => l.gameType === "blackjack").length },
    { value: "uno", label: "UNO", count: mockLobbies.filter(l => l.gameType === "uno").length },
    { value: "gofish", label: "Go Fish", count: 0 }
  ];

  const filteredLobbies = mockLobbies.filter(lobby => {
    const matchesSearch = lobby.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         lobby.gameType.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = selectedGameType === "all" || lobby.gameType === selectedGameType;
    return matchesSearch && matchesType;
  });

  const handleJoinLobby = (lobbyId: string) => {
    console.log("Joining lobby:", lobbyId);
    // TODO: Implement join lobby mutation
  };

  const handleCreateLobby = () => {
    setShowCreateLobby(true);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
  };

  const getGameTypeIcon = (gameType: string) => {
    switch (gameType) {
      case "poker": return "♠️";
      case "blackjack": return "🃏";
      case "uno": return "🎯";
      case "gofish": return "🐟";
      default: return "🎮";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "waiting": return "bg-green-500";
      case "in-progress": return "bg-yellow-500";
      case "full": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Header */}
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold">Game Lobbies</h1>
                <p className="text-muted-foreground">
                  Find and join multiplayer games
                </p>
              </div>
              
              <Button 
                variant="steam" 
                className="btn-glow"
                onClick={handleCreateLobby}
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Lobby
              </Button>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search lobbies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Game Type Tabs */}
          <Tabs value={selectedGameType} onValueChange={setSelectedGameType}>
            <TabsList className="grid grid-cols-5 w-full">
              {gameTypes.map((type) => (
                <TabsTrigger key={type.value} value={type.value} className="text-xs">
                  {type.label} ({type.count})
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={selectedGameType} className="space-y-6 mt-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="gradient-card">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Users className="h-8 w-8 text-blue-500" />
                      <div>
                        <p className="text-sm text-muted-foreground">Active Lobbies</p>
                        <p className="text-2xl font-bold">{filteredLobbies.length}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="gradient-card">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Gamepad2 className="h-8 w-8 text-green-500" />
                      <div>
                        <p className="text-sm text-muted-foreground">Players Online</p>
                        <p className="text-2xl font-bold">
                          {filteredLobbies.reduce((sum, lobby) => sum + lobby.currentPlayers, 0)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="gradient-card">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-8 w-8 text-yellow-500" />
                      <div>
                        <p className="text-sm text-muted-foreground">Waiting</p>
                        <p className="text-2xl font-bold">
                          {filteredLobbies.filter(l => l.status === "waiting").length}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="gradient-card">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Play className="h-8 w-8 text-purple-500" />
                      <div>
                        <p className="text-sm text-muted-foreground">In Progress</p>
                        <p className="text-2xl font-bold">
                          {filteredLobbies.filter(l => l.status === "in-progress").length}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Lobbies List */}
              {filteredLobbies.length > 0 ? (
                <div className="space-y-4">
                  {filteredLobbies.map((lobby) => (
                    <Card key={lobby.id} className="hover:bg-muted/50 transition-colors">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-start space-x-4">
                            {/* Game Type Icon */}
                            <div className="h-12 w-12 bg-primary/20 rounded-lg flex items-center justify-center text-2xl">
                              {getGameTypeIcon(lobby.gameType)}
                            </div>

                            {/* Lobby Info */}
                            <div className="space-y-2">
                              <div className="flex items-center space-x-3">
                                <h3 className="text-lg font-semibold">{lobby.name}</h3>
                                <Badge variant="outline" className="capitalize">
                                  {lobby.gameType}
                                </Badge>
                                <div className={`h-2 w-2 rounded-full ${getStatusColor(lobby.status)}`} />
                                <span className="text-sm text-muted-foreground capitalize">
                                  {lobby.status}
                                </span>
                              </div>

                              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                <div className="flex items-center space-x-1">
                                  <Crown className="h-4 w-4" />
                                  <span>{lobby.host.name}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Users className="h-4 w-4" />
                                  <span>{lobby.currentPlayers}/{lobby.maxPlayers}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Star className="h-4 w-4" />
                                  <span>{lobby.stakes} Stakes</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Clock className="h-4 w-4" />
                                  <span>{formatTimeAgo(lobby.createdAt)}</span>
                                </div>
                              </div>

                              {/* Players */}
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-muted-foreground">Players:</span>
                                <div className="flex -space-x-2">
                                  {lobby.players.map((player, index) => (
                                    <Avatar key={index} className="h-6 w-6 border-2 border-background">
                                      <AvatarImage src={player.avatar || undefined} />
                                      <AvatarFallback className="text-xs">
                                        {player.name.charAt(0)}
                                      </AvatarFallback>
                                    </Avatar>
                                  ))}
                                  {/* Empty slots */}
                                  {Array.from({ length: lobby.maxPlayers - lobby.currentPlayers }).map((_, index) => (
                                    <div 
                                      key={`empty-${index}`}
                                      className="h-6 w-6 rounded-full border-2 border-dashed border-muted-foreground/30 flex items-center justify-center"
                                    >
                                      <Plus className="h-3 w-3 text-muted-foreground/50" />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center space-x-2">
                            {lobby.status === "in-progress" ? (
                              <Button variant="outline" size="sm">
                                <Eye className="mr-2 h-4 w-4" />
                                Spectate
                              </Button>
                            ) : lobby.currentPlayers < lobby.maxPlayers ? (
                              <Button 
                                variant="steam" 
                                size="sm" 
                                className="btn-glow"
                                onClick={() => handleJoinLobby(lobby.id)}
                              >
                                <Users className="mr-2 h-4 w-4" />
                                Join Game
                              </Button>
                            ) : (
                              <Button variant="outline" size="sm" disabled>
                                Full
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Gamepad2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No lobbies found</h3>
                    <p className="text-muted-foreground mb-4">
                      {selectedGameType === "all" 
                        ? "No active lobbies right now. Create one to get started!"
                        : `No ${selectedGameType} lobbies available. Try a different game type.`
                      }
                    </p>
                    <Button variant="steam" className="btn-glow" onClick={handleCreateLobby}>
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Lobby
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          {/* Create Lobby Modal would go here */}
          {showCreateLobby && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <Card className="w-full max-w-md">
                <CardHeader>
                  <CardTitle>Create New Lobby</CardTitle>
                  <CardDescription>
                    Set up a new game session for other players to join
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Lobby Name</label>
                    <Input placeholder="Enter lobby name..." />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Game Type</label>
                    <select className="w-full px-3 py-2 bg-background border border-input rounded-md">
                      <option value="poker">Poker</option>
                      <option value="blackjack">Blackjack</option>
                      <option value="uno">UNO</option>
                      <option value="gofish">Go Fish</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Max Players</label>
                    <Input type="number" min="2" max="8" defaultValue="4" />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowCreateLobby(false)}>
                      Cancel
                    </Button>
                    <Button variant="steam" className="btn-glow">
                      Create Lobby
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}