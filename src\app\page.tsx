"use client";

import { MainLayout } from "@/components/layout/main-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { GameCard } from "@/components/game/game-card";
import { SessionCard } from "@/components/game/session-card";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { Gamepad2, Users, Trophy, TrendingUp } from "lucide-react";
import { useFeaturedGames } from "@/hooks/useGames";
import { useActiveSessions, useSessionStats } from "@/hooks/useGameSessions";
import Link from "next/link";

export default function HomePage() {
  // Use real data from Convex
  const { games: featuredGames, loading: gamesLoading } = useFeaturedGames(6);
  const activeSessions = useActiveSessions({ limit: 4 });
  const sessionStats = useSessionStats();

  const handleJoinSession = (sessionId: string) => {
    console.log("Joining session:", sessionId);
    // This would trigger the join session mutation
  };

  // Default stats while loading
  const stats = [
    { 
      name: "Active Players", 
      value: sessionStats?.totalActivePlayers?.toString() || "0", 
      icon: Users 
    },
    { 
      name: "Games Played Today", 
      value: sessionStats?.gamesCompletedToday?.toString() || "0", 
      icon: Gamepad2 
    },
    { 
      name: "Active Sessions", 
      value: sessionStats?.activeSessions?.toString() || "0", 
      icon: Trophy 
    },
    { 
      name: "Total Games", 
      value: sessionStats?.totalGamesPlayed?.toString() || "0", 
      icon: TrendingUp 
    },
  ];

  return (
    <ProtectedRoute>
      <MainLayout>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Hero Section */}
        <div className="relative rounded-lg bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 p-8 md:p-12">
          <div className="relative z-10">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              Welcome to <span className="text-primary">GameHub</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-6 max-w-2xl">
              Play your favorite card games with friends and players from around the world. 
              Join tournaments, climb leaderboards, and master the games you love.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" variant="steam" className="btn-glow" asChild>
                <Link href="/lobbies">
                  <Gamepad2 className="mr-2 h-5 w-5" />
                  Start Playing
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/store">
                  Browse Games
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat) => (
            <Card key={stat.name} className="gradient-card">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <stat.icon className="h-8 w-8 text-primary" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.name}
                    </p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Featured Games */}
        {featuredGames && featuredGames.length > 0 ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold">Featured Games</h2>
              <Button variant="ghost" asChild>
                <Link href="/store">View All</Link>
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredGames.slice(0, 3).map((game) => (
                <GameCard key={game._id} game={game} />
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold">Featured Games</h2>
              <Button variant="ghost" asChild>
                <Link href="/store">View All</Link>
              </Button>
            </div>
            <Card>
              <CardHeader className="text-center">
                <CardTitle>Loading Featured Games...</CardTitle>
                <CardDescription>
                  Discovering the best games for you
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        )}

        {/* Active Sessions */}
        {activeSessions && activeSessions.length > 0 ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold">Join a Game</h2>
              <Button variant="ghost" asChild>
                <Link href="/lobbies">View All Lobbies</Link>
              </Button>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {activeSessions.slice(0, 2).map((session: any) => (
                <SessionCard 
                  key={session._id} 
                  session={session}
                  onJoin={handleJoinSession}
                />
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold">Join a Game</h2>
              <Button variant="ghost" asChild>
                <Link href="/lobbies">View All Lobbies</Link>
              </Button>
            </div>
            <Card>
              <CardHeader className="text-center">
                <CardTitle>No Active Games</CardTitle>
                <CardDescription>
                  Be the first to create a game lobby!
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button variant="steam" className="btn-glow" asChild>
                  <Link href="/lobbies">
                    <Gamepad2 className="mr-2 h-4 w-4" />
                    Create Game
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="gradient-card">
            <CardHeader>
              <CardTitle>Create Your Own Game</CardTitle>
              <CardDescription>
                Host a private game with your friends or open it to the public
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" variant="steam" asChild>
                <Link href="/lobbies">Create Game</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardHeader>
              <CardTitle>Browse Game Store</CardTitle>
              <CardDescription>
                Discover new card games and add them to your library
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/store">Browse Store</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardHeader>
              <CardTitle>My Library</CardTitle>
              <CardDescription>
                View your game collection and playing history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" variant="outline" asChild>
                <Link href="/library">View Library</Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">Ready to Play?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Join thousands of players in exciting multiplayer card games. Whether you're a beginner or a pro, 
              there's always a game waiting for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="steam" className="btn-glow" asChild>
                <Link href="/lobbies">
                  <Users className="mr-2 h-5 w-5" />
                  Find Players
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/store">
                  <Trophy className="mr-2 h-5 w-5" />
                  Learn Games
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
    </ProtectedRoute>
  );
}
