# GameHub - Steam-like Gaming Platform

A comprehensive Steam-like gaming platform focused on multiplayer card games, built with Next.js, TypeScript, Tailwind CSS, and Convex as the backend database and real-time system.

## 🎮 Features

### ✅ Implemented
- **Complete Authentication System** - User registration, login, logout with Convex Auth
- **Modern UI/UX** - Steam-inspired design with dark theme and animations
- **Game Store** - Browse and discover card games with filtering and search
- **Game Library** - Personal game collection with stats and organization
- **User Profiles** - Comprehensive profile management with stats and preferences
- **Multiplayer Lobbies** - Find and join game sessions with real-time updates
- **Settings & Preferences** - Complete user settings management
- **Responsive Design** - Mobile-first approach with excellent mobile experience

### 🚧 Core Card Games
- **Texas Hold'em Poker** - Classic poker variant with betting rounds
- **Blackjack** - Casino-style card game against the dealer
- **UNO** - Family-friendly multiplayer card game
- **Go Fish** - Simple multiplayer card collection game

## 🏗️ Architecture

### Frontend Structure
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Main app pages
│   ├── games/             # Game-specific pages
│   ├── library/           # Game library
│   ├── lobbies/           # Multiplayer lobbies
│   ├── profile/           # User profile
│   ├── settings/          # User settings
│   ├── store/             # Game store
│   └── layout.tsx         # Root layout
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   ├── game/             # Game-specific components
│   ├── layout/           # Layout components
│   ├── auth/             # Authentication components
│   └── providers/        # Context providers
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── styles/               # Global styles
└── types/                # TypeScript definitions
```

### Backend Structure (Convex)
```
convex/
├── schema.ts             # Database schema definitions
├── auth.ts              # Authentication configuration
├── users.ts             # User management functions
├── games.ts             # Game catalog functions
├── gameSessions.ts      # Multiplayer session management
└── seed.ts              # Database seeding
```

## 🗄️ Database Schema

### Core Tables
- **users** - User profiles and authentication
- **games** - Game catalog with metadata
- **gameSessions** - Active multiplayer sessions
- **gameHistory** - Completed game records
- **friends** - Social connections
- **chatMessages** - In-game and global chat

### Key Features
- **Real-time Updates** - Live game state synchronization
- **Optimistic UI** - Instant feedback for user actions
- **Offline Support** - Graceful degradation when offline
- **Type Safety** - Full TypeScript coverage

## 🎨 Design System

### Color Palette
- **Primary**: Deep blue-gray (#1e2328)
- **Secondary**: Steam blue (#2a475e)
- **Accent**: Electric blue (#66c0f4)
- **Success**: Green (#4ade80)
- **Warning**: Orange (#fb923c)
- **Error**: Red (#ef4444)

### Component Features
- **Consistent Spacing** - 4px grid system
- **Typography Hierarchy** - 5 levels of text styling
- **Interactive States** - Hover, active, disabled states
- **Loading States** - Skeleton loaders and spinners
- **Steam-inspired** - Familiar gaming interface patterns

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Convex account

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd game-app
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up Convex**
```bash
npx convex dev
```

4. **Configure environment variables**
```bash
# Copy .env.example to .env.local
cp .env.example .env.local

# Add your Convex deployment URL
NEXT_PUBLIC_CONVEX_URL=your_convex_deployment_url

# Generate and add auth private key
openssl rand -base64 32
# Add to .env.local as AUTH_PRIVATE_KEY
```

5. **Seed the database (optional)**
```bash
npx convex function:run seed:seedUsers
npx convex function:run seed:seedGames
```

6. **Start the development server**
```bash
npm run dev
```

7. **Open your browser**
Navigate to `http://localhost:3000`

## 🎮 Demo Account
- **Email**: <EMAIL>
- **Password**: demo123

## 📱 Pages Overview

### 🏠 Homepage (`/`)
- **Hero Section** - Welcome message and quick actions
- **Live Stats** - Active players, games played, etc.
- **Featured Games** - Curated game recommendations
- **Active Sessions** - Join ongoing multiplayer games

### 🛒 Store (`/store`)
- **Game Catalog** - Browse all available games
- **Search & Filter** - Find games by type, rating, price
- **Game Details** - Screenshots, descriptions, reviews
- **Purchase System** - Buy games with virtual credits

### 📚 Library (`/library`)
- **Game Collection** - View owned games
- **Recently Played** - Quick access to recent games
- **Favorites** - Bookmarked games
- **Playtime Stats** - Track gaming activity

### 🎯 Lobbies (`/lobbies`)
- **Active Lobbies** - Find multiplayer games
- **Create Lobby** - Start new game sessions
- **Game Types** - Filter by poker, blackjack, UNO, etc.
- **Real-time Updates** - Live player counts and status

### 👤 Profile (`/profile`)
- **User Stats** - Games won, playtime, achievements
- **Activity Feed** - Recent gaming activity
- **Achievement System** - Unlock rewards
- **Social Features** - Friends and connections

### ⚙️ Settings (`/settings`)
- **Profile Management** - Update avatar, bio, email
- **Game Preferences** - Sound, notifications, auto-join
- **Privacy Settings** - Control visibility and data
- **Account Security** - Password, danger zone

## 🔧 Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Radix UI** - Accessible component primitives
- **Lucide React** - Beautiful icons

### Backend
- **Convex** - Real-time database and backend
- **Convex Auth** - Authentication system
- **TypeScript** - End-to-end type safety

### Development
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Husky** - Git hooks

## 🎯 Game Features

### Real-time Multiplayer
- **Turn-based Gameplay** - Synchronized game state
- **Live Chat** - In-game communication
- **Spectator Mode** - Watch ongoing games
- **Reconnection** - Resume interrupted games

### Card Game Mechanics
- **Poker** - Texas Hold'em with betting rounds
- **Blackjack** - Hit, stand, split, double down
- **UNO** - Special cards, draw mechanics
- **Go Fish** - Card collection and matching

### Social Features
- **Friends System** - Add and manage friends
- **Leaderboards** - Compete for top ranks
- **Achievements** - Unlock rewards and badges
- **Chat System** - Global and private messaging

## 🔐 Security Features

- **Password Hashing** - Secure credential storage
- **Session Management** - JWT-based authentication
- **Input Validation** - Prevent malicious data
- **Rate Limiting** - Prevent abuse and spam
- **Data Sanitization** - Clean user inputs

## 📊 Performance

- **Optimized Images** - WebP format with lazy loading
- **Code Splitting** - Dynamic imports for features
- **Caching Strategy** - Browser and CDN caching
- **Bundle Optimization** - Tree shaking and minification

## 🚧 Roadmap

### Phase 1: Core Features ✅
- [x] Authentication system
- [x] Basic UI components
- [x] Game catalog
- [x] User profiles
- [x] Settings management

### Phase 2: Multiplayer System 🚧
- [ ] Real-time game sessions
- [ ] Turn-based game logic
- [ ] Chat system
- [ ] Lobby management

### Phase 3: Advanced Features
- [ ] Achievement system
- [ ] Advanced matchmaking
- [ ] Tournament system
- [ ] Mobile app

### Phase 4: Polish & Launch
- [ ] Performance optimization
- [ ] Security hardening
- [ ] Comprehensive testing
- [ ] Production deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Convex Team** - For the amazing real-time backend
- **Vercel** - For hosting and deployment
- **Radix UI** - For accessible components
- **Tailwind CSS** - For the utility-first CSS framework

---

Built with ❤️ by the GameHub team
