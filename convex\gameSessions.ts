import { v } from "convex/values";
import { query, mutation } from "./_generated/server";

// Get all active game sessions
export const getActive = query({
  args: {
    gameType: v.optional(v.union(v.literal("poker"), v.literal("blackjack"), v.literal("uno"), v.literal("gofish"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    
    let query = ctx.db
      .query("gameSessions")
      .filter((q) => q.or(
        q.eq(q.field("status"), "waiting"),
        q.eq(q.field("status"), "active")
      ));

    const sessions = await query.order("desc").take(limit);
    
    // Get game details for each session
    const sessionsWithGameInfo = await Promise.all(
      sessions.map(async (session) => {
        const game = await ctx.db.get(session.gameId);
        return {
          ...session,
          gameTitle: game?.title || "Unknown Game",
          gameType: game?.gameType,
        };
      })
    );

    // Filter by game type if specified
    if (args.gameType) {
      return sessionsWithGameInfo.filter(session => session.gameType === args.gameType);
    }

    return sessionsWithGameInfo;
  },
});

// Get sessions by game ID
export const getByGame = query({
  args: {
    gameId: v.id("games"),
    status: v.optional(v.union(v.literal("waiting"), v.literal("active"), v.literal("finished"), v.literal("cancelled"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 10;
    
    let query = ctx.db
      .query("gameSessions")
      .filter((q) => q.eq(q.field("gameId"), args.gameId));

    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    return await query.order("desc").take(limit);
  },
});

// Get session by ID
export const get = query({
  args: { id: v.id("gameSessions") },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.id);
    if (!session) return null;

    const game = await ctx.db.get(session.gameId);
    return {
      ...session,
      gameTitle: game?.title || "Unknown Game",
      gameType: game?.gameType,
    };
  },
});

// Get sessions for a user
export const getUserSessions = query({
  args: {
    userId: v.id("users"),
    status: v.optional(v.union(v.literal("waiting"), v.literal("active"), v.literal("finished"), v.literal("cancelled"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    
    const sessions = await ctx.db.query("gameSessions").collect();
    
    // Filter sessions where user is a player
    let userSessions = sessions.filter(session => 
      session.players?.some(player => player.userId === args.userId)
    );

    // Filter by status if specified
    if (args.status) {
      userSessions = userSessions.filter(session => session.status === args.status);
    }

    // Sort by start time (most recent first) and limit
    userSessions.sort((a, b) => (b.startTime || 0) - (a.startTime || 0));
    userSessions = userSessions.slice(0, limit);

    // Get game details for each session
    return await Promise.all(
      userSessions.map(async (session) => {
        const game = await ctx.db.get(session.gameId);
        return {
          ...session,
          gameTitle: game?.title || "Unknown Game",
          gameType: game?.gameType,
        };
      })
    );
  },
});

// Create a new game session
export const create = mutation({
  args: {
    gameId: v.id("games"),
    hostId: v.id("users"),
    isPrivate: v.boolean(),
    password: v.optional(v.string()),
    maxPlayers: v.number(),
    turnTimeLimit: v.number(),
    settings: v.object({
      allowSpectators: v.boolean(),
      autoStart: v.boolean(),
      difficulty: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    // Get host user info
    const host = await ctx.db.get(args.hostId);
    if (!host) {
      throw new Error("Host user not found");
    }

    // Get game info
    const game = await ctx.db.get(args.gameId);
    if (!game || !game.isActive) {
      throw new Error("Game not found or inactive");
    }

    // Validate max players
    if (args.maxPlayers < game.minPlayers || args.maxPlayers > game.maxPlayers) {
      throw new Error(`Max players must be between ${game.minPlayers} and ${game.maxPlayers}`);
    }

    const sessionId = await ctx.db.insert("gameSessions", {
      gameId: args.gameId,
      hostId: args.hostId,
      players: [{
        userId: args.hostId,
        username: host.username,
        avatar: host.avatar,
        joinedAt: Date.now(),
        isReady: true,
        position: 0,
      }],
      spectators: [],
      status: "waiting",
      currentTurn: undefined,
      turnStartTime: undefined,
      turnTimeLimit: args.turnTimeLimit,
      gameState: undefined,
      startTime: undefined,
      endTime: undefined,
      winner: undefined,
      isPrivate: args.isPrivate,
      password: args.password,
      maxPlayers: args.maxPlayers,
      settings: args.settings,
    });

    return sessionId;
  },
});

// Join a game session
export const join = mutation({
  args: {
    sessionId: v.id("gameSessions"),
    userId: v.id("users"),
    password: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (session.status !== "waiting") {
      throw new Error("Cannot join session that is not waiting");
    }

    if (session.players.length >= session.maxPlayers) {
      throw new Error("Session is full");
    }

    // Check if user is already in the session
    if (session.players.some(p => p.userId === args.userId)) {
      throw new Error("User is already in this session");
    }

    // Check password for private sessions
    if (session.isPrivate && session.password !== args.password) {
      throw new Error("Invalid password");
    }

    // Get user info
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    const updatedPlayers = [...session.players, {
      userId: args.userId,
      username: user.username,
      avatar: user.avatar,
      joinedAt: Date.now(),
      isReady: false,
      position: session.players.length,
    }];

    await ctx.db.patch(args.sessionId, {
      players: updatedPlayers,
    });

    return args.sessionId;
  },
});

// Leave a game session
export const leave = mutation({
  args: {
    sessionId: v.id("gameSessions"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const playerIndex = session.players.findIndex(p => p.userId === args.userId);
    if (playerIndex === -1) {
      throw new Error("User is not in this session");
    }

    const updatedPlayers = session.players.filter(p => p.userId !== args.userId);

    // If this was the host and there are other players, transfer host to next player
    let newHostId = session.hostId;
    if (session.hostId === args.userId && updatedPlayers.length > 0) {
      newHostId = updatedPlayers[0]!.userId;
    }

    // If no players left, cancel the session
    if (updatedPlayers.length === 0) {
      await ctx.db.patch(args.sessionId, {
        status: "cancelled",
        endTime: Date.now(),
      });
    } else {
      await ctx.db.patch(args.sessionId, {
        players: updatedPlayers,
        hostId: newHostId,
      });
    }

    return args.sessionId;
  },
});

// Toggle ready status
export const toggleReady = mutation({
  args: {
    sessionId: v.id("gameSessions"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (session.status !== "waiting") {
      throw new Error("Cannot change ready status when game is not waiting");
    }

    const playerIndex = session.players.findIndex(p => p.userId === args.userId);
    if (playerIndex === -1) {
      throw new Error("User is not in this session");
    }

    const updatedPlayers = [...session.players];
    updatedPlayers[playerIndex]!.isReady = !updatedPlayers[playerIndex]!.isReady;

    await ctx.db.patch(args.sessionId, {
      players: updatedPlayers,
    });

    // Check if all players are ready and auto-start is enabled
    if (session.settings.autoStart && 
        updatedPlayers.length >= 2 && 
        updatedPlayers.every(p => p.isReady)) {
      await ctx.db.patch(args.sessionId, {
        status: "starting",
        startTime: Date.now(),
      });
    }

    return args.sessionId;
  },
});

// Start a game session (host only)
export const start = mutation({
  args: {
    sessionId: v.id("gameSessions"),
    hostId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (session.hostId !== args.hostId) {
      throw new Error("Only the host can start the game");
    }

    if (session.status !== "waiting") {
      throw new Error("Game is not in waiting status");
    }

    if (session.players.length < 2) {
      throw new Error("Need at least 2 players to start");
    }

    // Get the game to check minimum players
    const game = await ctx.db.get(session.gameId);
    if (game && session.players.length < game.minPlayers) {
      throw new Error(`Need at least ${game.minPlayers} players for this game`);
    }

    await ctx.db.patch(args.sessionId, {
      status: "active",
      startTime: Date.now(),
      currentTurn: 0,
      turnStartTime: Date.now(),
    });

    return args.sessionId;
  },
});

// End a game session
export const end = mutation({
  args: {
    sessionId: v.id("gameSessions"),
    winner: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    await ctx.db.patch(args.sessionId, {
      status: "finished",
      endTime: Date.now(),
      winner: args.winner,
    });

    // Update player statistics
    if (session.startTime) {
      const sessionDuration = Date.now() - session.startTime;
      const playtimeMinutes = Math.round(sessionDuration / 60000);

      for (const player of session.players) {
        const won = player.userId === args.winner;
        // This would call the users updateGameStats function
        // await ctx.runMutation("users:updateGameStats", {
        //   id: player.userId,
        //   won,
        //   playtime: playtimeMinutes,
        // });
      }
    }

    return args.sessionId;
  },
});

// Get session statistics
export const getStats = query({
  args: {},
  handler: async (ctx) => {
    const sessions = await ctx.db.query("gameSessions").collect();
    
    const activeSessions = sessions.filter(s => s.status === "active" || s.status === "waiting");
    const totalActivePlayers = activeSessions.reduce(
      (sum, session) => sum + (session.players?.length || 0),
      0
    );
    
    const completedToday = sessions.filter(s => {
      if (s.status !== "finished" || !s.endTime) return false;
      const today = new Date();
      const sessionDate = new Date(s.endTime);
      return sessionDate.toDateString() === today.toDateString();
    });

    return {
      activeSessions: activeSessions.length,
      totalActivePlayers,
      gamesCompletedToday: completedToday.length,
      totalGamesPlayed: sessions.filter(s => s.status === "finished").length,
    };
  },
});