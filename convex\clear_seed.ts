import { mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Comprehensive clear function that removes all seeded data from the database
 * This is the perfect mirror/opposite of the seed.ts file
 *
 * Clears data in the proper order to avoid foreign key constraint issues:
 * 1. Dependent records first (wishlists, notifications, chat messages, etc.)
 * 2. Relationship records (friends, user achievements, game reviews, etc.)
 * 3. Session and library records
 * 4. Core records last (users, games, achievements)
 */
export const clearAll = mutation({
  args: {},
  handler: async (ctx) => {
    const results = {
      wishlists: 0,
      notifications: 0,
      chatMessages: 0,
      friends: 0,
      gameReviews: 0,
      userAchievements: 0,
      gameSessions: 0,
      userLibrary: 0,
      achievements: 0,
      users: 0,
      games: 0,
      total: 0
    };

    try {
      // 1. CLEAR WISHLISTS
      const wishlists = await ctx.db.query("wishlists").collect();
      for (const wishlist of wishlists) {
        await ctx.db.delete(wishlist._id);
      }
      results.wishlists = wishlists.length;

      // 2. CLEAR NOTIFICATIONS
      const notifications = await ctx.db.query("notifications").collect();
      for (const notification of notifications) {
        await ctx.db.delete(notification._id);
      }
      results.notifications = notifications.length;

      // 3. CLEAR CHAT MESSAGES
      const chatMessages = await ctx.db.query("chatMessages").collect();
      for (const message of chatMessages) {
        await ctx.db.delete(message._id);
      }
      results.chatMessages = chatMessages.length;

      // 4. CLEAR FRIEND RELATIONSHIPS
      const friends = await ctx.db.query("friends").collect();
      for (const friend of friends) {
        await ctx.db.delete(friend._id);
      }
      results.friends = friends.length;

      // 5. CLEAR GAME REVIEWS
      const gameReviews = await ctx.db.query("gameReviews").collect();
      for (const review of gameReviews) {
        await ctx.db.delete(review._id);
      }
      results.gameReviews = gameReviews.length;

      // 6. CLEAR USER ACHIEVEMENTS
      const userAchievements = await ctx.db.query("userAchievements").collect();
      for (const userAchievement of userAchievements) {
        await ctx.db.delete(userAchievement._id);
      }
      results.userAchievements = userAchievements.length;

      // 7. CLEAR GAME SESSIONS
      const gameSessions = await ctx.db.query("gameSessions").collect();
      for (const session of gameSessions) {
        await ctx.db.delete(session._id);
      }
      results.gameSessions = gameSessions.length;

      // 8. CLEAR USER LIBRARY
      const userLibrary = await ctx.db.query("userLibrary").collect();
      for (const entry of userLibrary) {
        await ctx.db.delete(entry._id);
      }
      results.userLibrary = userLibrary.length;

      // 9. CLEAR ACHIEVEMENTS
      const achievements = await ctx.db.query("achievements").collect();
      for (const achievement of achievements) {
        await ctx.db.delete(achievement._id);
      }
      results.achievements = achievements.length;

      // 10. CLEAR USERS
      const users = await ctx.db.query("users").collect();
      for (const user of users) {
        await ctx.db.delete(user._id);
      }
      results.users = users.length;

      // 11. CLEAR GAMES (last, as other records may reference them)
      const games = await ctx.db.query("games").collect();
      for (const game of games) {
        await ctx.db.delete(game._id);
      }
      results.games = games.length;

      // Calculate total
      results.total = Object.values(results).reduce((sum, count) => sum + count, 0) - results.total;

      return {
        success: true,
        message: "Database successfully cleared of all seeded data!",
        results,
        summary: `Removed ${results.games} games, ${results.users} users, ${results.userLibrary} library entries, ${results.gameSessions} sessions, ${results.achievements} achievements, ${results.gameReviews} reviews, ${results.friends} friendships, ${results.chatMessages} messages, ${results.notifications} notifications, and ${results.wishlists} wishlist items. Total: ${results.total} records deleted.`
      };

    } catch (error) {
      return {
        success: false,
        message: `Clear operation failed: ${error}`,
        results
      };
    }
  },
});

/**
 * Quick clear function for development - clears only core tables
 * Use this when you want to quickly reset just the main data
 */
export const clearQuick = mutation({
  args: {},
  handler: async (ctx) => {
    let deletedCount = 0;

    try {
      // Clear only the main tables in safe order
      const tables = ["gameSessions", "userLibrary", "games", "users"];

      for (const tableName of tables) {
        const records = await ctx.db.query(tableName as any).collect();
        for (const record of records) {
          await ctx.db.delete(record._id);
          deletedCount++;
        }
      }

      return {
        success: true,
        message: `Quick clear completed: ${deletedCount} core records deleted`,
        deletedCount
      };

    } catch (error) {
      return {
        success: false,
        message: `Quick clear failed: ${error}`,
        deletedCount
      };
    }
  },
});

/**
 * Clear specific table - useful for targeted cleanup during development
 */
export const clearTable = mutation({
  args: { tableName: v.string() },
  handler: async (ctx, args) => {
    const { tableName } = args;

    try {
      const records = await ctx.db.query(tableName as any).collect();
      let deletedCount = 0;

      for (const record of records) {
        await ctx.db.delete(record._id);
        deletedCount++;
      }

      return {
        success: true,
        message: `Cleared table '${tableName}': ${deletedCount} records deleted`,
        tableName,
        deletedCount
      };

    } catch (error) {
      return {
        success: false,
        message: `Failed to clear table '${tableName}': ${error}`,
        tableName,
        deletedCount: 0
      };
    }
  },
});

/**
 * Get database status - shows current record counts for all tables
 * Useful to check what data exists before clearing
 */
export const getStatus = mutation({
  args: {},
  handler: async (ctx) => {
    const tables = [
      "games", "users", "userLibrary", "gameSessions",
      "achievements", "userAchievements", "gameReviews",
      "friends", "chatMessages", "notifications", "wishlists"
    ];

    const status: Record<string, number> = {};
    let totalRecords = 0;

    for (const tableName of tables) {
      try {
        const records = await ctx.db.query(tableName as any).collect();
        status[tableName] = records.length;
        totalRecords += records.length;
      } catch (error) {
        status[tableName] = 0; // Table might not exist
      }
    }

    const isEmpty = totalRecords === 0;

    return {
      status,
      totalRecords,
      isEmpty,
      message: isEmpty
        ? "Database is empty - ready for seeding!"
        : `Database contains ${totalRecords} records across ${Object.keys(status).length} tables`,
      summary: `Games: ${status.games}, Users: ${status.users}, Library: ${status.userLibrary}, Sessions: ${status.gameSessions}, Reviews: ${status.gameReviews}, Achievements: ${status.achievements}, Friends: ${status.friends}, Messages: ${status.chatMessages}, Notifications: ${status.notifications}, Wishlists: ${status.wishlists}`
    };
  },
});

// Legacy function names for backward compatibility
export const clearAllData = clearAll;
export const getDataCounts = getStatus;