// Basic types that don't depend on Convex generated types
export type GameType = "poker" | "blackjack" | "uno" | "gofish";
export type SessionStatus = "waiting" | "starting" | "active" | "paused" | "finished" | "cancelled";
export type MessageType = "global" | "private" | "game" | "system";
export type FriendStatus = "pending" | "accepted" | "blocked";
export type AchievementType = "progression" | "skill" | "social" | "hidden";
export type AchievementRarity = "common" | "uncommon" | "rare" | "legendary";
export type InvitationStatus = "pending" | "accepted" | "declined" | "expired";
export type NotificationType = "friend_request" | "game_invitation" | "achievement_unlocked" | "game_finished" | "system";

// Interface definitions that will be compatible with Convex types
export interface Player {
  userId: string;
  username: string;
  avatar?: string;
  joinedAt: number;
  isReady: boolean;
  position: number;
}

export interface Spectator {
  userId: string;
  username: string;
  joinedAt: number;
}

// UI state types
export interface GameFilters {
  genre?: string[];
  gameType?: GameType[];
  priceRange?: [number, number];
  rating?: number;
  playerCount?: [number, number];
  sortBy?: "title" | "rating" | "price" | "releaseDate" | "popularity";
  sortOrder?: "asc" | "desc";
}

export interface LobbyFilters {
  gameType?: GameType[];
  playerCount?: [number, number];
  hasPassword?: boolean;
  allowSpectators?: boolean;
  sortBy?: "created" | "players" | "game";
  sortOrder?: "asc" | "desc";
}

// Game state types
export interface PokerGameState {
  deck: string[];
  communityCards: string[];
  pot: number;
  currentBet: number;
  players: {
    [playerId: string]: {
      cards: string[];
      chips: number;
      bet: number;
      folded: boolean;
      allIn: boolean;
    };
  };
  phase: "preflop" | "flop" | "turn" | "river" | "showdown";
}

export interface BlackjackGameState {
  deck: string[];
  dealer: {
    cards: string[];
    value: number;
    hidden: boolean;
  };
  players: {
    [playerId: string]: {
      hands: Array<{
        cards: string[];
        value: number;
        bet: number;
        status: "playing" | "stand" | "bust" | "blackjack";
      }>;
      chips: number;
    };
  };
  phase: "betting" | "dealing" | "playing" | "dealer" | "finished";
}

export interface UnoGameState {
  deck: string[];
  discardPile: string[];
  currentCard: string;
  currentColor: string;
  direction: 1 | -1;
  players: {
    [playerId: string]: {
      cards: string[];
      hasCalledUno: boolean;
    };
  };
  drawCount: number;
}

export interface GoFishGameState {
  deck: string[];
  players: {
    [playerId: string]: {
      cards: string[];
      books: string[][];
    };
  };
  currentRequest?: {
    from: string;
    to: string;
    rank: string;
  };
}

// Component prop types
export interface GameCardProps {
  game: any; // Will be typed properly when Convex types are available
  isOwned?: boolean;
  onWishlist?: boolean;
  className?: string;
}

export interface UserCardProps {
  user: any; // Will be typed properly when Convex types are available
  showStatus?: boolean;
  showStats?: boolean;
  className?: string;
}

export interface SessionCardProps {
  session: any; // Will be typed properly when Convex types are available
  onJoin?: (sessionId: string) => void;
  className?: string;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  username: string;
  displayName: string;
  password: string;
  confirmPassword: string;
}

export interface CreateSessionForm {
  gameId: string;
  isPrivate: boolean;
  password?: string;
  maxPlayers: number;
  allowSpectators: boolean;
  autoStart: boolean;
  turnTimeLimit: number;
}

export interface EditProfileForm {
  displayName: string;
  bio: string;
  preferences: {
    notifications: boolean;
    publicProfile: boolean;
    showOnlineStatus: boolean;
    allowFriendRequests: boolean;
  };
}

// API response types
export interface PaginatedResponse<T> {
  items: T[];
  nextCursor?: string;
  hasMore: boolean;
  total?: number;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  query: string;
  facets?: Record<string, Array<{ value: string; count: number }>>;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Theme types
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    muted: string;
    success: string;
    warning: string;
    error: string;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
  shadows: Record<string, string>;
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  icon?: string;
  badge?: number;
  children?: NavItem[];
}

export interface Breadcrumb {
  title: string;
  href?: string;
}