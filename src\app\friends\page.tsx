"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Search, UserPlus, MessageSquare, Gamepad2, UserMinus, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useOnlineUsers, useUserSearch } from "@/hooks/useUsers";

export default function FriendsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  
  // Use real data from Convex
  const onlineUsers = useOnlineUsers(20);
  const searchResults = searchQuery ? useUserSearch(searchQuery, 10) : undefined;

  // Mock friends data - in real app this would come from a friends relationship table
  const mockFriends = [
    {
      _id: "friend-1",
      username: "PokerPro",
      displayName: "Alex the Poker Pro",
      avatar: "/api/placeholder/128/128",
      isOnline: true,
      lastActive: Date.now(),
      currentGame: "Texas Hold'em Poker",
      status: "In Game"
    },
    {
      _id: "friend-2", 
      username: "CardShark",
      displayName: "Sarah",
      avatar: "/api/placeholder/128/128",
      isOnline: true,
      lastActive: Date.now() - 300000,
      currentGame: null,
      status: "Online"
    },
    {
      _id: "friend-3",
      username: "UnoChamp",
      displayName: "Mike Johnson", 
      avatar: "/api/placeholder/128/128",
      isOnline: false,
      lastActive: Date.now() - 3600000,
      currentGame: null,
      status: "Offline"
    }
  ];

  const mockFriendRequests = [
    {
      _id: "request-1",
      fromUser: {
        username: "NewPlayer123",
        displayName: "Jamie Smith",
        avatar: "/api/placeholder/128/128"
      },
      sentAt: Date.now() - 86400000
    }
  ];

  const handleSendFriendRequest = (userId: string) => {
    console.log("Sending friend request to:", userId);
  };

  const handleAcceptFriendRequest = (requestId: string) => {
    console.log("Accepting friend request:", requestId);
  };

  const handleDeclineFriendRequest = (requestId: string) => {
    console.log("Declining friend request:", requestId);
  };

  const handleRemoveFriend = (friendId: string) => {
    console.log("Removing friend:", friendId);
  };

  const handleInviteToGame = (friendId: string) => {
    console.log("Inviting friend to game:", friendId);
  };

  const handleStartChat = (friendId: string) => {
    console.log("Starting chat with:", friendId);
  };

  const formatLastActive = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold">Friends</h1>
              <p className="text-muted-foreground">
                Connect with other players and build your gaming network
              </p>
            </div>
            <Button variant="steam" className="btn-glow">
              <UserPlus className="mr-2 h-4 w-4" />
              Find Players
            </Button>
          </div>

          {/* Search */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search players..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Tabs defaultValue="friends" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="friends">
              Friends ({mockFriends.length})
            </TabsTrigger>
            <TabsTrigger value="online">
              Online ({onlineUsers?.length || 0})
            </TabsTrigger>
            <TabsTrigger value="requests">
              Requests ({mockFriendRequests.length})
            </TabsTrigger>
            <TabsTrigger value="search">
              Find Players
            </TabsTrigger>
          </TabsList>

          {/* Friends List */}
          <TabsContent value="friends" className="space-y-4">
            {mockFriends.length === 0 ? (
              <Card>
                <CardHeader className="text-center">
                  <CardTitle>No friends yet</CardTitle>
                  <CardDescription>
                    Start building your gaming network by finding other players
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="steam" className="btn-glow">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Find Players
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {mockFriends.map((friend) => (
                  <Card key={friend._id} className="relative">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={friend.avatar} alt={friend.username} />
                              <AvatarFallback>
                                {friend.username.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            {friend.isOnline && (
                              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background"></div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold truncate">{friend.displayName}</h4>
                            <p className="text-sm text-muted-foreground truncate">
                              @{friend.username}
                            </p>
                          </div>
                        </div>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => handleStartChat(friend._id)}>
                              <MessageSquare className="mr-2 h-4 w-4" />
                              Send Message
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleInviteToGame(friend._id)}>
                              <Gamepad2 className="mr-2 h-4 w-4" />
                              Invite to Game
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleRemoveFriend(friend._id)}
                              className="text-destructive"
                            >
                              <UserMinus className="mr-2 h-4 w-4" />
                              Remove Friend
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className={`font-medium ${
                            friend.isOnline ? "text-green-500" : "text-muted-foreground"
                          }`}>
                            {friend.status}
                          </span>
                          <span className="text-muted-foreground">
                            {friend.isOnline ? "Now" : formatLastActive(friend.lastActive)}
                          </span>
                        </div>
                        
                        {friend.currentGame && (
                          <p className="text-sm text-muted-foreground">
                            Playing {friend.currentGame}
                          </p>
                        )}

                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            variant="outline" 
                            className="flex-1"
                            onClick={() => handleStartChat(friend._id)}
                          >
                            <MessageSquare className="mr-1 h-3 w-3" />
                            Chat
                          </Button>
                          <Button 
                            size="sm" 
                            variant="steam" 
                            className="flex-1"
                            onClick={() => handleInviteToGame(friend._id)}
                            disabled={!friend.isOnline}
                          >
                            <Gamepad2 className="mr-1 h-3 w-3" />
                            Invite
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Online Players */}
          <TabsContent value="online" className="space-y-4">
            {onlineUsers && onlineUsers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {onlineUsers.map((user: any) => (
                  <Card key={user._id}>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="relative">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.avatar} alt={user.username} />
                            <AvatarFallback>
                              {user.username?.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background"></div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-semibold truncate">{user.displayName}</h4>
                          <p className="text-sm text-muted-foreground truncate">
                            @{user.username}
                          </p>
                        </div>
                      </div>
                      
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="w-full"
                        onClick={() => handleSendFriendRequest(user._id)}
                      >
                        <UserPlus className="mr-2 h-3 w-3" />
                        Add Friend
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardHeader className="text-center">
                  <CardTitle>No players online</CardTitle>
                  <CardDescription>
                    Check back later to see who's playing
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>

          {/* Friend Requests */}
          <TabsContent value="requests" className="space-y-4">
            {mockFriendRequests.length === 0 ? (
              <Card>
                <CardHeader className="text-center">
                  <CardTitle>No friend requests</CardTitle>
                  <CardDescription>
                    Friend requests will appear here
                  </CardDescription>
                </CardHeader>
              </Card>
            ) : (
              <div className="space-y-4">
                {mockFriendRequests.map((request) => (
                  <Card key={request._id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={request.fromUser.avatar} alt={request.fromUser.username} />
                            <AvatarFallback>
                              {request.fromUser.username.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-semibold">{request.fromUser.displayName}</h4>
                            <p className="text-sm text-muted-foreground">
                              @{request.fromUser.username} • {formatLastActive(request.sentAt)}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleDeclineFriendRequest(request._id)}
                          >
                            Decline
                          </Button>
                          <Button 
                            size="sm" 
                            variant="steam"
                            onClick={() => handleAcceptFriendRequest(request._id)}
                          >
                            Accept
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Search Players */}
          <TabsContent value="search" className="space-y-4">
            {searchQuery ? (
              searchResults && searchResults.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {searchResults.map((user: any) => (
                    <Card key={user._id}>
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3 mb-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.avatar} alt={user.username} />
                            <AvatarFallback>
                              {user.username?.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold truncate">{user.displayName}</h4>
                            <p className="text-sm text-muted-foreground truncate">
                              @{user.username}
                            </p>
                          </div>
                        </div>
                        
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="w-full"
                          onClick={() => handleSendFriendRequest(user._id)}
                        >
                          <UserPlus className="mr-2 h-3 w-3" />
                          Add Friend
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardHeader className="text-center">
                    <CardTitle>No players found</CardTitle>
                    <CardDescription>
                      Try searching with a different username
                    </CardDescription>
                  </CardHeader>
                </Card>
              )
            ) : (
              <Card>
                <CardHeader className="text-center">
                  <CardTitle>Search for Players</CardTitle>
                  <CardDescription>
                    Enter a username in the search box above to find other players
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}