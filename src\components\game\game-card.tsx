"use client";

import * as React from "react";
import Image from "next/image";
import Link from "next/link";
import { Star, Heart, ShoppingCart, Users, Clock } from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn, formatCurrency, getRatingStars } from "@/lib/utils";
import type { GameCardProps } from "@/types";

export function GameCard({ 
  game, 
  isOwned = false, 
  onWishlist = false, 
  className 
}: GameCardProps) {
  const [isHovered, setIsHovered] = React.useState(false);
  const { full, half, empty } = getRatingStars(game.rating || 0);

  return (
    <Card 
      className={cn(
        "game-card overflow-hidden group cursor-pointer",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/games/${game._id}`}>
        <div className="relative aspect-[16/9] overflow-hidden">
          <Image
            src={game.images?.header || "/api/placeholder/400/225"}
            alt={game.title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110"
          />
          {/* Overlay with additional info on hover */}
          <div className={cn(
            "absolute inset-0 bg-black/60 flex items-center justify-center transition-opacity duration-300",
            isHovered ? "opacity-100" : "opacity-0"
          )}>
            <div className="text-center text-white p-4">
              <p className="text-sm mb-2">{game.shortDescription}</p>
              <div className="flex items-center justify-center space-x-4 text-xs">
                <div className="flex items-center">
                  <Users className="w-3 h-3 mr-1" />
                  {game.minPlayers}-{game.maxPlayers} players
                </div>
                <div className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {game.estimatedPlayTime}min
                </div>
              </div>
            </div>
          </div>
          
          {/* Game type badge */}
          <div className="absolute top-2 left-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/90 text-primary-foreground capitalize">
              {game.gameType}
            </span>
          </div>

          {/* Featured badge */}
          {game.featured && (
            <div className="absolute top-2 right-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/90 text-yellow-900">
                Featured
              </span>
            </div>
          )}
        </div>
      </Link>

      <CardContent className="p-4">
        <div className="space-y-2">
          <div className="flex items-start justify-between">
            <h3 className="font-semibold text-lg truncate flex-1 mr-2">
              {game.title}
            </h3>
            <Button
              variant="ghost"
              size="icon"
              className="shrink-0 text-muted-foreground hover:text-destructive"
              onClick={(e) => {
                e.preventDefault();
                // TODO: Toggle wishlist
              }}
            >
              <Heart className={cn(
                "h-4 w-4",
                onWishlist && "fill-current text-destructive"
              )} />
            </Button>
          </div>

          <p className="text-sm text-muted-foreground line-clamp-2">
            {game.description}
          </p>

          {/* Rating */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              {Array.from({ length: full }, (_, i) => (
                <Star key={`full-${i}`} className="w-4 h-4 fill-current text-yellow-400" />
              ))}
              {half && (
                <Star className="w-4 h-4 fill-current text-yellow-400 opacity-50" />
              )}
              {Array.from({ length: empty }, (_, i) => (
                <Star key={`empty-${i}`} className="w-4 h-4 text-gray-600" />
              ))}
            </div>
            <span className="text-sm text-muted-foreground">
              ({game.reviewCount || 0})
            </span>
          </div>

          {/* Tags/Genres */}
          <div className="flex flex-wrap gap-1">
            {game.genre?.slice(0, 3).map((tag: string) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-muted text-muted-foreground"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-lg font-bold">
            {game.price === 0 ? "Free" : formatCurrency(game.price)}
          </span>
          {game.price > 0 && (
            <span className="text-sm text-muted-foreground line-through">
              {formatCurrency(game.price * 1.2)}
            </span>
          )}
        </div>

        {isOwned ? (
          <Button variant="secondary" size="sm">
            In Library
          </Button>
        ) : (
          <Button variant="steam" size="sm" className="btn-glow">
            <ShoppingCart className="w-4 h-4 mr-2" />
            {game.price === 0 ? "Get" : "Buy"}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}