"use client";

import * as React from "react";
import { <PERSON>, <PERSON>, Eye, Clock, Play } from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn, getGameStatusColor, getPlayerCount, formatRelativeTime } from "@/lib/utils";
import type { SessionCardProps } from "@/types";

export function SessionCard({ session, onJoin, className }: SessionCardProps) {
  const [isHovered, setIsHovered] = React.useState(false);
  
  const statusColor = getGameStatusColor(session.status || "waiting");
  const canJoin = session.status === "waiting" && session.players?.length < session.maxPlayers;
  const isFull = session.players?.length >= session.maxPlayers;

  return (
    <Card 
      className={cn(
        "game-card overflow-hidden cursor-pointer border-l-4",
        session.status === "waiting" && "border-l-yellow-500",
        session.status === "active" && "border-l-green-500",
        session.status === "finished" && "border-l-blue-500",
        session.status === "cancelled" && "border-l-red-500",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <h3 className="font-semibold text-lg">
                {session.gameTitle || "Card Game"}
              </h3>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <span className={cn("capitalize", statusColor)}>
                  {session.status}
                </span>
                {session.isPrivate && (
                  <div className="flex items-center">
                    <Lock className="w-3 h-3 mr-1" />
                    Private
                  </div>
                )}
                {session.settings?.allowSpectators && (
                  <div className="flex items-center">
                    <Eye className="w-3 h-3 mr-1" />
                    Spectators OK
                  </div>
                )}
              </div>
            </div>
            
            <div className="text-right text-sm text-muted-foreground">
              <div className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {formatRelativeTime(session.startTime || Date.now())}
              </div>
            </div>
          </div>

          {/* Players */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Players</span>
              <span className="text-sm text-muted-foreground">
                {getPlayerCount(session.players?.length || 0, session.maxPlayers)}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {session.players?.slice(0, 6).map((player: any, index: number) => (
                <div key={player.userId} className="relative group">
                  <Avatar className="h-8 w-8 border-2 border-background">
                    <AvatarImage src={player.avatar} alt={player.username} />
                    <AvatarFallback className="text-xs">
                      {player.username.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  {player.isReady && session.status === "waiting" && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border border-background"></div>
                  )}
                  {/* Tooltip on hover */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-popover text-popover-foreground text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                    {player.username}
                    {player.isReady && session.status === "waiting" && " (Ready)"}
                  </div>
                </div>
              ))}
              
              {session.players && session.players.length > 6 && (
                <div className="text-xs text-muted-foreground">
                  +{session.players.length - 6} more
                </div>
              )}
              
              {/* Empty slots */}
              {Array.from({ 
                length: Math.max(0, Math.min(4, session.maxPlayers - (session.players?.length || 0)))
              }).map((_, index) => (
                <div 
                  key={`empty-${index}`}
                  className="w-8 h-8 rounded-full border-2 border-dashed border-muted-foreground/30 flex items-center justify-center"
                >
                  <Users className="w-3 h-3 text-muted-foreground/50" />
                </div>
              ))}
            </div>
          </div>

          {/* Spectators (if any) */}
          {session.spectators && session.spectators.length > 0 && (
            <div className="space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Spectators ({session.spectators.length})
              </span>
              <div className="flex items-center space-x-1">
                {session.spectators.slice(0, 4).map((spectator: any) => (
                  <Avatar key={spectator.userId} className="h-6 w-6">
                    <AvatarFallback className="text-xs">
                      {spectator.username.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {session.spectators.length > 4 && (
                  <span className="text-xs text-muted-foreground ml-1">
                    +{session.spectators.length - 4}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Game Settings */}
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            {session.turnTimeLimit && (
              <div className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {session.turnTimeLimit}s turns
              </div>
            )}
            {session.settings?.difficulty && (
              <div className="capitalize">
                {session.settings.difficulty} difficulty
              </div>
            )}
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Host: <span className="text-foreground font-medium">
            {session.players?.find((p: any) => p.userId === session.hostId)?.username || "Unknown"}
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {session.status === "active" && session.settings?.allowSpectators && (
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-2" />
              Watch
            </Button>
          )}
          
          {canJoin && (
            <Button 
              variant="steam" 
              size="sm" 
              className="btn-glow"
              onClick={() => onJoin?.(session._id)}
              disabled={isFull}
            >
              <Play className="w-4 h-4 mr-2" />
              {isFull ? "Full" : "Join"}
            </Button>
          )}
          
          {session.status === "waiting" && !canJoin && !isFull && (
            <Button variant="outline" size="sm" disabled>
              Waiting
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}