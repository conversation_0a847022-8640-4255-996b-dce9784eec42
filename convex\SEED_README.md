# GameHub Database Seeding

This directory contains unified seeding and clearing functions for the GameHub database.

## Files

- **`seed.ts`** - Comprehensive seeding function that populates the database with demo data
- **`clear_seed.ts`** - Mirror functions that clear all seeded data from the database

## Usage

### Seeding the Database

To populate your database with comprehensive demo data:

```bash
# In Convex dashboard or via CLI
npx convex run seed:seedAll
```

This will create:
- 4 card games (Poker, Blackjack, UNO, Go Fish)
- 5 demo users with realistic stats
- User libraries with game ownership
- Active game sessions
- Achievements system
- Game reviews and ratings
- Friend relationships
- Chat messages
- Notifications
- Wishlist items

### Clearing the Database

To remove all seeded data:

```bash
# Complete clear (recommended)
npx convex run clear_seed:clearAll

# Quick clear (core tables only)
npx convex run clear_seed:clearQuick

# Clear specific table
npx convex run clear_seed:clearTable '{"tableName": "games"}'
```

### Checking Database Status

To see current record counts:

```bash
npx convex run clear_seed:getStatus
```

## Demo Data Overview

### Users Created
1. **PokerPro** (<EMAIL>) - Experienced poker player
2. **CardShark** (<EMAIL>) - Blackjack specialist  
3. **UnoChamp** (<EMAIL>) - Casual UNO player
4. **LuckyPlayer** (<EMAIL>) - New player exploring games
5. **DemoUser** (<EMAIL>) - Demo account for testing

### Games Available
- **Texas Hold'em Poker** (2-9 players, 45min avg)
- **Blackjack** (1-7 players, 15min avg)
- **UNO** (2-8 players, 20min avg)
- **Go Fish** (2-6 players, 10min avg)

### Relationships
- Alex and Sarah are friends
- Emma and Demo user are friends
- Mike has a pending friend request to Alex

### Active Sessions
- Poker game with Alex and Sarah (waiting for more players)
- Blackjack game with Sarah (waiting to start)

## Development Workflow

1. **Start Fresh**: `npx convex run clear_seed:clearAll`
2. **Check Status**: `npx convex run clear_seed:getStatus`
3. **Seed Data**: `npx convex run seed:seedAll`
4. **Develop/Test**: Work with realistic demo data
5. **Reset When Needed**: Repeat steps 1-3

## Safety Features

- **Duplicate Protection**: Seeding checks for existing data and won't duplicate
- **Proper Ordering**: Clear functions delete in correct order to avoid constraint issues
- **Error Handling**: Both functions include comprehensive error handling
- **Detailed Reporting**: Functions return detailed results about what was created/deleted

## Function Reference

### seed.ts
- `seedAll()` - Main seeding function (creates everything)

### clear_seed.ts  
- `clearAll()` - Complete database clear (recommended)
- `clearQuick()` - Clear core tables only (faster)
- `clearTable(tableName)` - Clear specific table
- `getStatus()` - Get current database status
- `clearAllData()` - Legacy alias for clearAll()
- `getDataCounts()` - Legacy alias for getStatus()

## Notes

- All demo data uses placeholder images (`/api/placeholder/...`)
- User passwords are managed by Convex Auth (not included in seed data)
- Timestamps are relative to current time for realistic "recent activity"
- All functions are mutations and can be called from the Convex dashboard
