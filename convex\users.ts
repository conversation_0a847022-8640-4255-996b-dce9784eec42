import { v } from "convex/values";
import { query, mutation } from "./_generated/server";

// Get user by ID
export const get = query({
  args: { id: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get user by email
export const getByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
  },
});

// Get user by username
export const getByUsername = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_username", (q) => q.eq("username", args.username))
      .unique();
  },
});

// Get multiple users by IDs
export const getMany = query({
  args: { ids: v.array(v.id("users")) },
  handler: async (ctx, args) => {
    const users = await Promise.all(
      args.ids.map((id) => ctx.db.get(id))
    );
    return users.filter((user): user is any => user !== null);
  },
});

// Search users by username or display name
export const search = query({
  args: { 
    query: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;
    const query = args.query.toLowerCase();
    
    const users = await ctx.db.query("users").collect();
    
    return users
      .filter((user) => 
        user.username.toLowerCase().includes(query) ||
        user.displayName.toLowerCase().includes(query)
      )
      .slice(0, limit);
  },
});

// Get online users
export const getOnlineUsers = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    return await ctx.db
      .query("users")
      .withIndex("by_online_status", (q) => q.eq("isOnline", true))
      .order("desc")
      .take(limit);
  },
});

// Create a new user
export const create = mutation({
  args: {
    email: v.string(),
    username: v.string(),
    displayName: v.string(),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if email already exists
    const existingEmail = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
    
    if (existingEmail) {
      throw new Error("Email already exists");
    }

    // Check if username already exists
    const existingUsername = await ctx.db
      .query("users")
      .withIndex("by_username", (q) => q.eq("username", args.username))
      .unique();
    
    if (existingUsername) {
      throw new Error("Username already exists");
    }

    const now = Date.now();
    
    return await ctx.db.insert("users", {
      email: args.email,
      username: args.username,
      displayName: args.displayName,
      avatar: args.avatar,
      bio: "",
      joinDate: now,
      isOnline: true,
      lastActive: now,
      gamesPlayed: 0,
      gamesWon: 0,
      totalPlaytime: 0,
      preferences: {
        notifications: true,
        soundEnabled: true,
        autoJoinGames: false,
        theme: "dark",
      },
      credits: 1000, // Starting credits
    });
  },
});

// Update user profile
export const updateProfile = mutation({
  args: {
    id: v.id("users"),
    displayName: v.optional(v.string()),
    bio: v.optional(v.string()),
    avatar: v.optional(v.string()),
    preferences: v.optional(v.object({
      notifications: v.boolean(),
      publicProfile: v.boolean(),
      showOnlineStatus: v.boolean(),
      allowFriendRequests: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id);
    if (!user) {
      throw new Error("User not found");
    }

    const updates: Partial<any> = {};
    
    if (args.displayName !== undefined) {
      updates.displayName = args.displayName;
    }
    if (args.bio !== undefined) {
      updates.bio = args.bio;
    }
    if (args.avatar !== undefined) {
      updates.avatar = args.avatar;
    }
    if (args.preferences !== undefined) {
      updates.preferences = args.preferences;
    }

    await ctx.db.patch(args.id, updates);
    return args.id;
  },
});

// Update user online status
export const updateOnlineStatus = mutation({
  args: {
    id: v.id("users"),
    isOnline: v.boolean(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id);
    if (!user) {
      throw new Error("User not found");
    }

    await ctx.db.patch(args.id, {
      isOnline: args.isOnline,
      lastActive: Date.now(),
    });
    
    return args.id;
  },
});

// Update user stats after a game
export const updateGameStats = mutation({
  args: {
    id: v.id("users"),
    won: v.boolean(),
    playtime: v.number(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id);
    if (!user) {
      throw new Error("User not found");
    }

    await ctx.db.patch(args.id, {
      gamesPlayed: user.gamesPlayed + 1,
      gamesWon: args.won ? user.gamesWon + 1 : user.gamesWon,
      totalPlaytime: user.totalPlaytime + args.playtime,
    });
    
    return args.id;
  },
});

// Add credits to user
export const addCredits = mutation({
  args: {
    id: v.id("users"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id);
    if (!user) {
      throw new Error("User not found");
    }

    await ctx.db.patch(args.id, {
      credits: user.credits + args.amount,
    });
    
    return user.credits + args.amount;
  },
});

// Spend credits
export const spendCredits = mutation({
  args: {
    id: v.id("users"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id);
    if (!user) {
      throw new Error("User not found");
    }

    if (user.credits < args.amount) {
      throw new Error("Insufficient credits");
    }

    const newCredits = user.credits - args.amount;
    await ctx.db.patch(args.id, {
      credits: newCredits,
    });
    
    return newCredits;
  },
});

// Get user statistics
export const getStats = query({
  args: { id: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id);
    if (!user) {
      return null;
    }

    // Get additional statistics
    const achievements = await ctx.db
      .query("userAchievements")
      .withIndex("by_user", (q) => q.eq("userId", args.id))
      .filter((q) => q.eq(q.field("isCompleted"), true))
      .collect();

    const friends = await ctx.db
      .query("friends")
      .withIndex("by_user", (q) => q.eq("userId", args.id))
      .filter((q) => q.eq(q.field("status"), "accepted"))
      .collect();

    const library = await ctx.db
      .query("userLibrary")
      .withIndex("by_user", (q) => q.eq("userId", args.id))
      .collect();

    const winRate = user.gamesPlayed > 0 
      ? Math.round((user.gamesWon / user.gamesPlayed) * 100) 
      : 0;

    return {
      gamesPlayed: user.gamesPlayed,
      gamesWon: user.gamesWon,
      winRate,
      totalPlaytime: user.totalPlaytime,
      achievementsUnlocked: achievements.length,
      friendsCount: friends.length,
      gamesOwned: library.length,
      credits: user.credits,
      joinDate: user.joinDate,
    };
  },
});

// Delete user (soft delete by marking inactive)
export const deleteUser = mutation({
  args: { id: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.id);
    if (!user) {
      throw new Error("User not found");
    }

    // In a real app, you might want to soft delete or anonymize the user
    // For now, we'll just mark them as offline
    await ctx.db.patch(args.id, {
      isOnline: false,
      lastActive: Date.now(),
    });
    
    return args.id;
  },
});