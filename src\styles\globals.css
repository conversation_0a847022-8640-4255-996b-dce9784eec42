@import "tailwindcss";

@theme {
  --font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  /* Steam-inspired Dark Theme */
  --color-background: 15 16 20; /* Very dark blue-gray */
  --color-foreground: 229 231 235; /* Light gray */
  --color-card: 23 26 31; /* Dark blue-gray */
  --color-card-foreground: 229 231 235;
  --color-popover: 23 26 31;
  --color-popover-foreground: 229 231 235;
  
  --color-primary: 102 192 244; /* Steam blue */
  --color-primary-foreground: 15 16 20;
  --color-secondary: 42 71 94; /* Darker blue */
  --color-secondary-foreground: 229 231 235;
  --color-muted: 55 65 81; /* Gray */
  --color-muted-foreground: 156 163 175;
  --color-accent: 66 192 244; /* Electric blue accent */
  --color-accent-foreground: 15 16 20;
  
  --color-destructive: 239 68 68; /* Red */
  --color-destructive-foreground: 254 242 242;
  --color-success: 74 222 128; /* Green */
  --color-success-foreground: 15 16 20;
  --color-warning: 251 146 60; /* Orange */
  --color-warning-foreground: 15 16 20;
  
  --color-border: 55 65 81;
  --color-input: 55 65 81;
  --color-ring: 102 192 244;
  
  --radius: 0.5rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(23 26 31);
}

::-webkit-scrollbar-thumb {
  background: rgb(55 65 81);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(75 85 99);
}

/* Smooth transitions */
* {
  @apply transition-colors duration-200;
}

/* Custom gradients */
.gradient-steam {
  background: linear-gradient(135deg, rgb(42 71 94) 0%, rgb(23 26 31) 100%);
}

.gradient-card {
  background: linear-gradient(135deg, rgb(30 35 40) 0%, rgb(23 26 31) 100%);
}

/* Game card hover effects */
.game-card {
  @apply transition-all duration-300 ease-in-out;
}

.game-card:hover {
  @apply transform scale-105 shadow-2xl;
}

/* Button glow effect */
.btn-glow {
  box-shadow: 0 0 20px rgba(102, 192, 244, 0.3);
}

.btn-glow:hover {
  box-shadow: 0 0 30px rgba(102, 192, 244, 0.5);
}

/* Loading animations */
@keyframes pulse-steam {
  0%, 100% {
    opacity: 1;
    background-color: rgb(42 71 94);
  }
  50% {
    opacity: 0.7;
    background-color: rgb(102 192 244);
  }
}

.loading-steam {
  animation: pulse-steam 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Card shine effect */
@keyframes shine {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.card-shine {
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: shine 3s infinite;
}

/* Game status indicators */
.status-waiting {
  @apply bg-yellow-500/20 text-yellow-400 border-yellow-500/30;
}

.status-active {
  @apply bg-green-500/20 text-green-400 border-green-500/30;
}

.status-finished {
  @apply bg-blue-500/20 text-blue-400 border-blue-500/30;
}

.status-cancelled {
  @apply bg-red-500/20 text-red-400 border-red-500/30;
}

/* Rating stars */
.star-filled {
  @apply text-yellow-400;
}

.star-empty {
  @apply text-gray-600;
}

/* Online status indicator */
.online-indicator {
  @apply relative;
}

.online-indicator::after {
  content: '';
  @apply absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-background;
}

/* Chat message styling */
.chat-message {
  @apply p-2 rounded-lg mb-2 max-w-md;
}

.chat-message.own {
  @apply bg-primary/20 ml-auto text-right;
}

.chat-message.other {
  @apply bg-muted/50;
}

.chat-message.system {
  @apply bg-accent/20 text-accent text-center mx-auto text-sm italic;
}
