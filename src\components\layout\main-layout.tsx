"use client";

import * as React from "react";
import { Header } from "./header";
import { Sidebar } from "./sidebar";
import { cn } from "@/lib/utils";

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function MainLayout({ children, className }: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-background font-sans antialiased">
      <Header />
      <div className="flex">
        <aside className="hidden lg:block w-64 border-r bg-card/30">
          <Sidebar />
        </aside>
        <main className={cn("flex-1 lg:pl-0", className)}>
          {children}
        </main>
      </div>
    </div>
  );
}