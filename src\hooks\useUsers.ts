"use client";

import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

export function useUser(id: string) {
  return useQuery(api.users.get, { id: id as any });
}

export function useUserByEmail(email: string) {
  return useQuery(api.users.getByEmail, { email });
}

export function useUserByUsername(username: string) {
  return useQuery(api.users.getByUsername, { username });
}

export function useUserSearch(query: string, limit?: number) {
  return useQuery(api.users.search, { query, limit });
}

export function useOnlineUsers(limit?: number) {
  return useQuery(api.users.getOnlineUsers, { limit });
}

export function useUserStats(id: string) {
  return useQuery(api.users.getStats, { id: id as any });
}

// Mutations
export function useCreateUser() {
  return useMutation(api.users.create);
}

export function useUpdateProfile() {
  return useMutation(api.users.updateProfile);
}

export function useUpdateOnlineStatus() {
  return useMutation(api.users.updateOnlineStatus);
}

export function useUpdateGameStats() {
  return useMutation(api.users.updateGameStats);
}

export function useAddCredits() {
  return useMutation(api.users.addCredits);
}

export function useSpendCredits() {
  return useMutation(api.users.spendCredits);
}